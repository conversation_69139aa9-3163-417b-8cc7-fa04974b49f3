# Advanced Admin Script - Simple Admin Replacement

Ein umfassendes FiveM Admin-Script, das als vollwertiger Ersatz für Simple Admin entwickelt wurde.

## Features

### 🛡️ Admin-Befehle
- **Kick/Ban System**: Spieler kicken und bannen mit Gründen
- **Teleportation**: <PERSON><PERSON>, <PERSON><PERSON>lern oder vordefinierten Orten teleportieren
- **Player Management**: <PERSON><PERSON>n, Rüstung geben, einfrieren, God Mode
- **Vehicle Management**: Fahrzeuge spawnen, reparieren, löschen
- **Server Controls**: Wetter und Zeit ändern, Ankündigungen senden

### 🎮 Benutzerfreundliche UI
- **Modernes Interface**: Responsive HTML/CSS/JS Admin-Menü
- **Spieler-Management**: Übersichtliche Spielerliste mit Kontextmenü
- **Tab-Navigation**: Organisierte Bereiche für verschiedene Funktionen
- **Echtzeit-Updates**: Live-Updates für Spielerliste und Logs

### 🔐 Erweiterte Berechtigungen
- **Rollen-System**: Verschiedene Admin-Level (Superadmin, Admin, Moderator, Helper)
- **Flexible Konfiguration**: Einfache Anpassung der Berechtigungen
- **Identifier-Support**: Steam ID und License ID Unterstützung

### 📊 Logging & Monitoring
- **Umfassendes Logging**: Alle Admin-Aktionen werden protokolliert
- **File & Database Logging**: Wahlweise in Dateien oder Datenbank
- **Log-Suche**: Durchsuchbare Log-Historie
- **Automatische Rotation**: Verhindert zu große Log-Dateien

## Installation

1. **Download**: Lade das Script in deinen `resources` Ordner
2. **Konfiguration**: Bearbeite `config.lua` nach deinen Wünschen
3. **Server.cfg**: Füge `ensure advanced_admin` zu deiner server.cfg hinzu
4. **Admins hinzufügen**: Trage Admin-Identifiers in die Config ein

### Beispiel Config
```lua
Config.Admins = {
    ['steam:***************'] = 'superadmin',
    ['license:abcdef1234567890'] = 'admin',
}
```

## Befehle

### Admin-Befehle
| Befehl | Berechtigung | Beschreibung |
|--------|--------------|--------------|
| `/admin` | helper | Öffnet das Admin-Menü |
| `/kick <id> [grund]` | moderator | Kickt einen Spieler |
| `/ban <id> [tage] [grund]` | admin | Bannt einen Spieler |
| `/unban <identifier>` | admin | Entbannt einen Spieler |
| `/tp <x> <y> <z>` | moderator | Teleportiert zu Koordinaten |
| `/tpto <id>` | moderator | Teleportiert zu einem Spieler |
| `/tphere <id>` | moderator | Teleportiert Spieler zu dir |
| `/heal [id]` | moderator | Heilt Spieler |
| `/armor [id]` | moderator | Gibt Rüstung |
| `/god [id]` | admin | Togglet God Mode |
| `/noclip` | admin | Togglet NoClip |
| `/car <name>` | moderator | Spawnt Fahrzeug |
| `/fix` | moderator | Repariert Fahrzeug |
| `/weather <type>` | admin | Ändert Wetter |
| `/time <hour> [minute]` | admin | Ändert Zeit |

### Utility-Befehle
| Befehl | Beschreibung |
|--------|--------------|
| `/coords` | Zeigt aktuelle Koordinaten |
| `/tpwp` | Teleportiert zum Waypoint |
| `/savepos [name]` | Speichert Position |
| `/loadpos [name]` | Lädt gespeicherte Position |
| `/spectate <id>` | Spectatet einen Spieler |

## Konfiguration

### Berechtigungslevel
```lua
Config.PermissionLevels = {
    ['superadmin'] = 100,
    ['admin'] = 80,
    ['moderator'] = 60,
    ['helper'] = 40,
    ['vip'] = 20,
    ['user'] = 0
}
```

### Logging-Einstellungen
```lua
Config.Logging = {
    logFile = 'logs/admin_actions.log',
    logFormat = '[%s] %s: %s',
    maxLogSize = ********, -- 10MB
}
```

### UI-Anpassung
```lua
Config.UI = {
    position = 'top-right',
    theme = 'dark',
    showNotifications = true,
    notificationDuration = 5000
}
```

## Datenbank-Integration (Optional)

Für erweiterte Features kann eine MySQL-Datenbank verwendet werden:

1. **mysql-async** installieren
2. `Config.LogToDatabase = true` setzen
3. Tabellen werden automatisch erstellt

### Benötigte Tabellen
- `admin_logs`: Speichert alle Admin-Aktionen
- `admin_bans`: Speichert Ban-Informationen
- `admin_permissions`: Speichert Admin-Berechtigungen

## NoClip-Steuerung

| Taste | Aktion |
|-------|--------|
| W/S | Vorwärts/Rückwärts |
| A/D | Links/Rechts |
| Q/E | Hoch/Runter |
| Shift | Schneller |
| Alt | Langsamer |

## API/Exports

### Server-Exports
```lua
-- Berechtigung prüfen
local hasPermission = exports['advanced_admin']:HasPermission(playerId, 'admin')

-- Spieler bannen
local banId = exports['advanced_admin']:BanPlayer(targetId, adminId, reason, duration)

-- Admin hinzufügen
exports['advanced_admin']:AddAdmin(identifier, role)
```

### Client-Exports
```lua
-- Admin-Menü öffnen
exports['advanced_admin']:OpenAdminMenu()

-- NoClip starten
exports['advanced_admin']:StartNoclip()
```

## Anpassung

### Neue Befehle hinzufügen
1. Server-Befehl in `server/commands.lua` registrieren
2. Client-Handler in `client/main.lua` hinzufügen (falls nötig)
3. UI-Button in `html/index.html` erstellen (optional)

### Neue Teleport-Orte
```lua
Config.TeleportLocations = {
    ['spawn'] = { x = -1037.0, y = -2737.0, z = 20.0, heading = 0.0 },
    ['custom'] = { x = 100.0, y = 200.0, z = 30.0, heading = 90.0 },
}
```

## Troubleshooting

### Häufige Probleme
1. **Admin-Menü öffnet nicht**: Überprüfe Berechtigungen in der Config
2. **Befehle funktionieren nicht**: Stelle sicher, dass das Script gestartet ist
3. **NoClip funktioniert nicht**: Überprüfe Client-Script Fehler in F8

### Debug-Modus
Setze `Config.Debug = true` für erweiterte Konsolen-Ausgaben.

## Support

Bei Problemen oder Fragen:
1. Überprüfe die Konsole auf Fehlermeldungen
2. Stelle sicher, dass alle Dateien korrekt installiert sind
3. Überprüfe die Konfiguration

## Changelog

### Version 1.0.0
- Initiale Veröffentlichung
- Vollständiges Admin-System
- Moderne UI
- Umfassendes Logging
- Datenbank-Integration

## Lizenz

Dieses Script ist für den privaten und kommerziellen Gebrauch freigegeben.

---

**Entwickelt als vollwertiger Simple Admin Ersatz für FiveM Server**
