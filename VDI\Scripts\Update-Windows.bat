@echo off
:: ====================================================================
:: Windows Update Script
:: Installiert alle verfügbaren Windows Updates
:: ====================================================================

echo.
echo ====================================================================
echo                        WINDOWS UPDATES
echo ====================================================================
echo.

:: PowerShell Execution Policy temporär setzen
echo [INFO] Setze PowerShell Execution Policy...
powershell -Command "Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser -Force"

:: PSWindowsUpdate Modul prüfen und installieren
echo [INFO] Pruefe PSWindowsUpdate Modul...
powershell -Command "& {if (!(Get-Module -ListAvailable -Name PSWindowsUpdate)) { Install-Module -Name PSWindowsUpdate -Force -AllowClobber -Scope CurrentUser }}"

if %errorLevel% neq 0 (
    echo [INFO] PSWindowsUpdate Modul Installation fehlgeschlagen. Verwende alternative Methode...
    goto ALTERNATIVE_METHOD
)

echo [INFO] PSWindowsUpdate Modul verfuegbar.
echo.

:: Windows Updates suchen
echo [INFO] Suche nach verfuegbaren Windows Updates...
powershell -Command "& {Import-Module PSWindowsUpdate; Get-WindowsUpdate -MicrosoftUpdate}"
echo.

:: Windows Updates installieren
echo [INFO] Installiere Windows Updates...
echo [WARNUNG] Dies kann einige Zeit dauern...
echo.

powershell -Command "& {Import-Module PSWindowsUpdate; Get-WindowsUpdate -MicrosoftUpdate -Install -AcceptAll -AutoReboot:$false -Verbose}"

if %errorLevel% equ 0 (
    echo [SUCCESS] Windows Updates erfolgreich installiert!
) else (
    echo [WARNUNG] Einige Updates konnten nicht installiert werden oder erfordern einen Neustart.
)

goto CHECK_REBOOT

:ALTERNATIVE_METHOD
echo [INFO] Verwende Windows Update API...

:: Windows Update über WUAUCLT (ältere Methode)
echo [INFO] Starte Windows Update Service...
net start wuauserv >nul 2>&1

echo [INFO] Suche nach Updates ueber Windows Update Agent...
wuauclt /detectnow
timeout /t 10 /nobreak >nul

wuauclt /updatenow
echo [INFO] Windows Update Agent gestartet.

:: Alternative: PowerShell ohne PSWindowsUpdate Modul
echo [INFO] Verwende Windows Update COM-Objekt...
powershell -Command "& { $updateSession = New-Object -ComObject Microsoft.Update.Session; $updateSearcher = $updateSession.CreateUpdateSearcher(); $searchResult = $updateSearcher.Search('IsInstalled=0'); if ($searchResult.Updates.Count -gt 0) { $updateCollection = New-Object -ComObject Microsoft.Update.UpdateColl; foreach ($update in $searchResult.Updates) { $updateCollection.Add($update) }; $installer = $updateSession.CreateUpdateInstaller(); $installer.Updates = $updateCollection; $installationResult = $installer.Install(); Write-Host 'Installation Result:' $installationResult.ResultCode } else { Write-Host 'Keine Updates verfuegbar.' } }"

:CHECK_REBOOT
echo.
echo [INFO] Pruefe ob Neustart erforderlich ist...

:: Prüfen ob Neustart erforderlich
powershell -Command "& {if ((Get-ChildItem 'HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\WindowsUpdate\Auto Update\RebootRequired' -ErrorAction SilentlyContinue) -or (Get-ChildItem 'HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Component Based Servicing\RebootPending' -ErrorAction SilentlyContinue)) { Write-Host '[WARNUNG] Neustart erforderlich fuer Windows Updates!' } else { Write-Host '[INFO] Kein Neustart erforderlich.' }}"

:: Windows Defender Updates
echo.
echo [INFO] Aktualisiere Windows Defender...
powershell -Command "& {Update-MpSignature -Verbose}" 2>nul
if %errorLevel% equ 0 (
    echo [SUCCESS] Windows Defender Signaturen aktualisiert!
) else (
    echo [INFO] Windows Defender Update nicht verfuegbar oder bereits aktuell.
)

:: Microsoft Store Apps updaten
echo.
echo [INFO] Aktualisiere Microsoft Store Apps...
powershell -Command "& {Get-CimInstance -Namespace 'Root\cimv2\mdm\dmmap' -ClassName 'MDM_EnterpriseModernAppManagement_AppManagement01' | Invoke-CimMethod -MethodName UpdateScanMethod}" 2>nul

:: Winget für Microsoft Store Apps
winget upgrade --source msstore --silent --accept-package-agreements --accept-source-agreements 2>nul

echo [INFO] Microsoft Store Apps Update gestartet.

:: .NET Framework Updates prüfen
echo.
echo [INFO] Pruefe .NET Framework Updates...
powershell -Command "& {Get-WindowsUpdate -MicrosoftUpdate | Where-Object {$_.Title -like '*.NET*'} | Install-WindowsUpdate -AcceptAll -AutoReboot:$false}" 2>nul

:: Visual C++ Redistributables updaten
echo [INFO] Aktualisiere Visual C++ Redistributables...
winget upgrade --id Microsoft.VCRedist.2015+.x64 --silent --accept-package-agreements --accept-source-agreements 2>nul
winget upgrade --id Microsoft.VCRedist.2015+.x86 --silent --accept-package-agreements --accept-source-agreements 2>nul

echo.
echo [INFO] Windows Updates abgeschlossen!
echo.

:: Update-Historie anzeigen
echo [INFO] Letzte installierte Updates:
powershell -Command "& {Get-HotFix | Sort-Object InstalledOn -Descending | Select-Object -First 5 | Format-Table HotFixID, Description, InstalledOn -AutoSize}"

echo.
echo [INFO] Windows Update Prozess abgeschlossen!
echo.

exit /b 0
