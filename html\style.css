/* Advanced Admin Menu Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: transparent;
    color: #ffffff;
    overflow: hidden;
}

.hidden {
    display: none !important;
}

/* Notifications */
#notifications {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    max-width: 400px;
}

.notification {
    background: rgba(0, 0, 0, 0.9);
    border-left: 4px solid #007bff;
    padding: 15px 20px;
    margin-bottom: 10px;
    border-radius: 5px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    animation: slideIn 0.3s ease-out;
    position: relative;
    overflow: hidden;
}

.notification.success {
    border-left-color: #28a745;
}

.notification.error {
    border-left-color: #dc3545;
}

.notification.warning {
    border-left-color: #ffc107;
}

.notification.info {
    border-left-color: #17a2b8;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Admin Menu */
.admin-menu {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 900px;
    max-height: 700px;
    background: linear-gradient(135deg, rgba(20, 20, 30, 0.95), rgba(30, 30, 40, 0.95));
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 1000;
    animation: menuSlideIn 0.3s ease-out;
}

@keyframes menuSlideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

.menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.2);
    border-radius: 15px 15px 0 0;
}

.menu-header h2 {
    color: #ffffff;
    font-size: 24px;
    font-weight: 600;
}

.close-btn {
    background: rgba(220, 53, 69, 0.2);
    border: 1px solid #dc3545;
    color: #dc3545;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: #dc3545;
    color: white;
}

.menu-content {
    padding: 0;
    max-height: 600px;
    overflow: hidden;
}

/* Navigation Tabs */
.nav-tabs {
    display: flex;
    background: rgba(0, 0, 0, 0.3);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.tab-btn {
    flex: 1;
    padding: 15px 20px;
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
}

.tab-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
}

.tab-btn.active {
    background: rgba(0, 123, 255, 0.2);
    color: #007bff;
    border-bottom: 2px solid #007bff;
}

/* Tab Content */
.tab-content {
    padding: 25px;
    max-height: 500px;
    overflow-y: auto;
}

.tab-content::-webkit-scrollbar {
    width: 8px;
}

.tab-content::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

.tab-content::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
}

.tab-content::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* Control Groups */
.control-group {
    margin-bottom: 25px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.control-group h3 {
    margin-bottom: 15px;
    color: #007bff;
    font-size: 16px;
    font-weight: 600;
}

/* Form Elements */
input, select, textarea {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    padding: 10px 15px;
    border-radius: 8px;
    margin-right: 10px;
    margin-bottom: 10px;
    font-size: 14px;
    transition: all 0.3s ease;
}

input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.2);
}

input::placeholder, textarea::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

textarea {
    width: 100%;
    min-height: 80px;
    resize: vertical;
}

/* Buttons */
button {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    margin-right: 10px;
    margin-bottom: 10px;
}

button:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d, #545b62);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #545b62, #3d4142);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
}

/* Search Bar */
.search-bar {
    display: flex;
    margin-bottom: 20px;
    align-items: center;
}

.search-bar input {
    flex: 1;
    margin-right: 10px;
}

/* Player List */
.player-list {
    max-height: 400px;
    overflow-y: auto;
}

.player-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    margin-bottom: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
}

.player-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
}

.player-info {
    display: flex;
    align-items: center;
}

.player-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #007bff, #0056b3);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-weight: bold;
}

.player-details h4 {
    margin-bottom: 5px;
    font-size: 16px;
}

.player-details span {
    color: rgba(255, 255, 255, 0.7);
    font-size: 12px;
}

.player-actions {
    display: flex;
    gap: 5px;
}

.player-actions button {
    padding: 5px 10px;
    font-size: 12px;
    margin: 0;
}

/* Grids */
.vehicle-grid, .location-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
    margin-top: 15px;
}

.vehicle-btn, .location-btn {
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    font-size: 14px;
}

.vehicle-btn:hover, .location-btn:hover {
    background: rgba(0, 123, 255, 0.2);
    border-color: #007bff;
    transform: translateY(-2px);
}

/* Context Menu */
.context-menu {
    position: fixed;
    background: rgba(20, 20, 30, 0.95);
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 2000;
    min-width: 180px;
    backdrop-filter: blur(10px);
}

.context-item {
    padding: 12px 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.context-item:last-child {
    border-bottom: none;
}

.context-item:hover {
    background: rgba(0, 123, 255, 0.2);
    color: #007bff;
}

.context-item i {
    margin-right: 10px;
    width: 16px;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 3000;
}

.modal-content {
    background: linear-gradient(135deg, rgba(20, 20, 30, 0.95), rgba(30, 30, 40, 0.95));
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.1);
    min-width: 400px;
    max-width: 500px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-body {
    padding: 25px;
}

.modal-body input, .modal-body textarea {
    width: 100%;
    margin: 0;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 20px 25px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Logs */
.logs-container {
    max-height: 350px;
    overflow-y: auto;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    padding: 15px;
}

.log-entry {
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    font-family: 'Courier New', monospace;
    font-size: 12px;
}

.log-entry:last-child {
    border-bottom: none;
}

.log-timestamp {
    color: #007bff;
    margin-right: 10px;
}

.log-player {
    color: #28a745;
    margin-right: 10px;
}

.log-action {
    color: rgba(255, 255, 255, 0.8);
}
