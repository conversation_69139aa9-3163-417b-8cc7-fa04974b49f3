@echo off
:: ====================================================================
:: Auto Update Master Script
:: Automatisches Update aller wichtigen Programme und Windows
:: ====================================================================

title Auto Update Master - Alle Programme aktualisieren
color 0A

echo.
echo ====================================================================
echo                    AUTO UPDATE MASTER SCRIPT
echo ====================================================================
echo.
echo Dieses Script aktualisiert automatisch:
echo - Windows Updates
echo - Winget Programme (alle verfuegbaren Updates)
echo - Firefox
echo - Microsoft Office
echo - Adobe Reader
echo - Weitere wichtige Programme
echo.
echo ====================================================================
echo.

:: Admin-Rechte prüfen
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo [FEHLER] Dieses Script benoetigt Administrator-Rechte!
    echo Bitte als Administrator ausfuehren.
    echo.
    pause
    exit /b 1
)

echo [INFO] Administrator-Rechte erkannt. Starte Updates...
echo.

:: Logfile erstellen
set "LOGFILE=%~dp0Logs\AutoUpdate_%date:~-4,4%-%date:~-7,2%-%date:~-10,2%_%time:~0,2%-%time:~3,2%-%time:~6,2%.log"
if not exist "%~dp0Logs" mkdir "%~dp0Logs"

echo ====================================================================>> "%LOGFILE%"
echo Auto Update Master Script gestartet am %date% um %time%>> "%LOGFILE%"
echo ====================================================================>> "%LOGFILE%"

:: Startzeit merken
set START_TIME=%time%

echo [1/7] Starte Winget Updates...
call "%~dp0Scripts\Update-Winget.bat" >> "%LOGFILE%" 2>&1

echo [2/7] Starte Firefox Update...
call "%~dp0Scripts\Update-Firefox.bat" >> "%LOGFILE%" 2>&1

echo [3/7] Starte Office Update...
call "%~dp0Scripts\Update-Office.bat" >> "%LOGFILE%" 2>&1

echo [4/7] Starte Adobe Reader Update...
call "%~dp0Scripts\Update-Adobe.bat" >> "%LOGFILE%" 2>&1

echo [5/7] Starte Windows Updates...
call "%~dp0Scripts\Update-Windows.bat" >> "%LOGFILE%" 2>&1

echo [6/7] Starte zusaetzliche Programme...
call "%~dp0Scripts\Update-Additional.bat" >> "%LOGFILE%" 2>&1

echo [7/7] Bereinige temporaere Dateien...
call "%~dp0Scripts\Cleanup-System.bat" >> "%LOGFILE%" 2>&1

:: Endzeit berechnen
set END_TIME=%time%

echo.
echo ====================================================================
echo                        UPDATE ABGESCHLOSSEN
echo ====================================================================
echo.
echo Startzeit: %START_TIME%
echo Endzeit:   %END_TIME%
echo.
echo Logfile: %LOGFILE%
echo.
echo Alle Updates wurden ausgefuehrt!
echo.

:: Zusammenfassung ins Log schreiben
echo.>> "%LOGFILE%"
echo ====================================================================>> "%LOGFILE%"
echo Update-Prozess abgeschlossen am %date% um %time%>> "%LOGFILE%"
echo Startzeit: %START_TIME%>> "%LOGFILE%"
echo Endzeit: %END_TIME%>> "%LOGFILE%"
echo ====================================================================>> "%LOGFILE%"

:: Fragen ob Neustart gewünscht
echo.
set /p RESTART="Moechten Sie den Computer neu starten? (j/n): "
if /i "%RESTART%"=="j" (
    echo Computer wird in 10 Sekunden neu gestartet...
    shutdown /r /t 10 /c "Auto Update Master - Neustart nach Updates"
) else (
    echo.
    echo Updates abgeschlossen. Druecken Sie eine beliebige Taste zum Beenden.
    pause >nul
)

exit /b 0
