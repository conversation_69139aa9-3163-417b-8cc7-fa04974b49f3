@echo off
:: ====================================================================
:: System Cleanup Script
:: Bereinigt temporäre Dateien und System-Cache
:: ====================================================================

echo.
echo ====================================================================
echo                        SYSTEM BEREINIGUNG
echo ====================================================================
echo.

:: Temporäre Dateien bereinigen
echo [INFO] Bereinige temporaere Dateien...

:: Windows Temp-Ordner
if exist "%TEMP%" (
    echo [INFO] Bereinige Windows Temp-Ordner...
    del /f /s /q "%TEMP%\*.*" >nul 2>&1
    for /d %%i in ("%TEMP%\*") do rmdir /s /q "%%i" >nul 2>&1
)

:: System Temp-Ordner
if exist "%SystemRoot%\Temp" (
    echo [INFO] Bereinige System Temp-Ordner...
    del /f /s /q "%SystemRoot%\Temp\*.*" >nul 2>&1
    for /d %%i in ("%SystemRoot%\Temp\*") do rmdir /s /q "%%i" >nul 2>&1
)

:: Prefetch-Dateien bereinigen
if exist "%SystemRoot%\Prefetch" (
    echo [INFO] Bereinige Prefetch-Dateien...
    del /f /q "%SystemRoot%\Prefetch\*.*" >nul 2>&1
)

:: Browser-Cache bereinigen
echo [INFO] Bereinige Browser-Cache...

:: Chrome Cache
if exist "%LOCALAPPDATA%\Google\Chrome\User Data\Default\Cache" (
    rmdir /s /q "%LOCALAPPDATA%\Google\Chrome\User Data\Default\Cache" >nul 2>&1
    echo [INFO] Chrome Cache bereinigt.
)

:: Firefox Cache (bereits in Firefox-Update-Script)
if exist "%LOCALAPPDATA%\Mozilla\Firefox\Profiles" (
    for /d %%i in ("%LOCALAPPDATA%\Mozilla\Firefox\Profiles\*") do (
        if exist "%%i\cache2" rmdir /s /q "%%i\cache2" >nul 2>&1
    )
    echo [INFO] Firefox Cache bereinigt.
)

:: Edge Cache
if exist "%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\Cache" (
    rmdir /s /q "%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\Cache" >nul 2>&1
    echo [INFO] Edge Cache bereinigt.
)

:: Windows Update Cache
echo [INFO] Bereinige Windows Update Cache...
dism /online /cleanup-image /startcomponentcleanup /resetbase >nul 2>&1

:: Disk Cleanup ausführen
echo [INFO] Starte Disk Cleanup...
cleanmgr /sagerun:1 >nul 2>&1

:: Windows Store Cache
echo [INFO] Bereinige Windows Store Cache...
wsreset >nul 2>&1

:: DNS Cache leeren
echo [INFO] Leere DNS Cache...
ipconfig /flushdns >nul 2>&1

:: Thumbnail Cache bereinigen
echo [INFO] Bereinige Thumbnail Cache...
if exist "%LOCALAPPDATA%\Microsoft\Windows\Explorer" (
    del /f /q "%LOCALAPPDATA%\Microsoft\Windows\Explorer\thumbcache_*.db" >nul 2>&1
)

:: Recent Documents bereinigen
echo [INFO] Bereinige Recent Documents...
if exist "%APPDATA%\Microsoft\Windows\Recent" (
    del /f /q "%APPDATA%\Microsoft\Windows\Recent\*.*" >nul 2>&1
)

:: Recycle Bin leeren
echo [INFO] Leere Papierkorb...
powershell -Command "& {Clear-RecycleBin -Force -ErrorAction SilentlyContinue}" >nul 2>&1

:: Event Logs bereinigen (optional)
echo [INFO] Bereinige Event Logs...
for /f "tokens=*" %%i in ('wevtutil el') do (
    wevtutil cl "%%i" >nul 2>&1
)

:: System File Checker
echo [INFO] Fuehre System File Check aus...
sfc /scannow >nul 2>&1

:: Memory Diagnostic (für nächsten Neustart planen)
echo [INFO] Plane Memory Diagnostic fuer naechsten Neustart...
mdsched /f >nul 2>&1

:: Windows Search Index neu aufbauen (optional)
echo [INFO] Setze Windows Search Index zurueck...
sc stop "WSearch" >nul 2>&1
timeout /t 5 /nobreak >nul
sc start "WSearch" >nul 2>&1

:: Registry bereinigen (vorsichtig)
echo [INFO] Bereinige Registry temporaere Eintraege...
reg delete "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\RunMRU" /f >nul 2>&1
reg delete "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\TypedPaths" /f >nul 2>&1

:: Defragmentierung planen (nur für HDDs)
echo [INFO] Pruefe Festplatten-Optimierung...
defrag C: /A >nul 2>&1

:: Windows Defender Cache bereinigen
echo [INFO] Bereinige Windows Defender Cache...
if exist "%ProgramData%\Microsoft\Windows Defender\Scans\History" (
    rmdir /s /q "%ProgramData%\Microsoft\Windows Defender\Scans\History" >nul 2>&1
)

:: Office Cache bereinigen (falls vorhanden)
if exist "%LOCALAPPDATA%\Microsoft\Office" (
    echo [INFO] Bereinige Office Cache...
    for /d %%i in ("%LOCALAPPDATA%\Microsoft\Office\*") do (
        if exist "%%i\OfficeFileCache" rmdir /s /q "%%i\OfficeFileCache" >nul 2>&1
    )
)

:: Adobe Cache bereinigen
if exist "%LOCALAPPDATA%\Adobe" (
    echo [INFO] Bereinige Adobe Cache...
    for /d %%i in ("%LOCALAPPDATA%\Adobe\*") do (
        if exist "%%i\Cache" rmdir /s /q "%%i\Cache" >nul 2>&1
    )
)

:: Java Cache bereinigen
if exist "%LOCALAPPDATA%\Sun\Java\Deployment\cache" (
    echo [INFO] Bereinige Java Cache...
    rmdir /s /q "%LOCALAPPDATA%\Sun\Java\Deployment\cache" >nul 2>&1
)

:: Steam Cache bereinigen (falls vorhanden)
if exist "%ProgramFiles(x86)%\Steam\appcache" (
    echo [INFO] Bereinige Steam Cache...
    rmdir /s /q "%ProgramFiles(x86)%\Steam\appcache" >nul 2>&1
)

:: Speicherplatz-Analyse
echo.
echo [INFO] Speicherplatz-Analyse:
powershell -Command "& {Get-WmiObject -Class Win32_LogicalDisk | Where-Object {$_.DriveType -eq 3} | ForEach-Object {$freespace = [math]::Round($_.FreeSpace/1GB,2); $totalspace = [math]::Round($_.Size/1GB,2); $usedspace = $totalspace - $freespace; Write-Host \"Laufwerk $($_.DeviceID) - Gesamt: $totalspace GB, Belegt: $usedspace GB, Frei: $freespace GB\"}}"

echo.
echo [INFO] System-Bereinigung abgeschlossen!
echo [INFO] Empfehlung: Starten Sie den Computer neu fuer optimale Leistung.
echo.

exit /b 0
