@echo off
:: ====================================================================
:: Microsoft Office Update Script
:: Aktualisiert Microsoft Office auf die neueste Version
:: ====================================================================

echo.
echo ====================================================================
echo                        OFFICE UPDATE
echo ====================================================================
echo.

:: Office Installation prüfen
set "OFFICE_FOUND=0"
set "OFFICE_PATH="

:: Office 365/2019/2021 (Click-to-Run) prüfen
if exist "%ProgramFiles%\Microsoft Office\root\Office16\WINWORD.EXE" (
    set "OFFICE_PATH=%ProgramFiles%\Microsoft Office\root\Office16"
    set "OFFICE_FOUND=1"
    echo [INFO] Office 365/2019/2021 gefunden: %OFFICE_PATH%
) else if exist "%ProgramFiles(x86)%\Microsoft Office\root\Office16\WINWORD.EXE" (
    set "OFFICE_PATH=%ProgramFiles(x86)%\Microsoft Office\root\Office16"
    set "OFFICE_FOUND=1"
    echo [INFO] Office 365/2019/2021 gefunden: %OFFICE_PATH%
)

:: Office 2016 prüfen
if %OFFICE_FOUND% equ 0 (
    if exist "%ProgramFiles%\Microsoft Office\Office16\WINWORD.EXE" (
        set "OFFICE_PATH=%ProgramFiles%\Microsoft Office\Office16"
        set "OFFICE_FOUND=1"
        echo [INFO] Office 2016 gefunden: %OFFICE_PATH%
    ) else if exist "%ProgramFiles(x86)%\Microsoft Office\Office16\WINWORD.EXE" (
        set "OFFICE_PATH=%ProgramFiles(x86)%\Microsoft Office\Office16"
        set "OFFICE_FOUND=1"
        echo [INFO] Office 2016 gefunden: %OFFICE_PATH%
    )
)

if %OFFICE_FOUND% equ 0 (
    echo [INFO] Microsoft Office ist nicht installiert.
    echo [INFO] Versuche Office ueber Winget zu installieren...
    winget install --id Microsoft.Office --silent --accept-package-agreements --accept-source-agreements
    goto END
)

:: Office Version anzeigen
echo [INFO] Aktuelle Office Version:
if exist "%OFFICE_PATH%\WINWORD.EXE" (
    powershell -Command "& {(Get-ItemProperty '%OFFICE_PATH%\WINWORD.EXE').VersionInfo.FileVersion}"
)
echo.

:: Office über Click-to-Run updaten (bevorzugte Methode für moderne Office-Versionen)
echo [INFO] Suche nach Office Click-to-Run Updates...

:: OfficeC2RClient.exe für Updates verwenden
set "C2R_CLIENT="
if exist "%ProgramFiles%\Common Files\Microsoft Shared\ClickToRun\OfficeC2RClient.exe" (
    set "C2R_CLIENT=%ProgramFiles%\Common Files\Microsoft Shared\ClickToRun\OfficeC2RClient.exe"
) else if exist "%ProgramFiles(x86)%\Common Files\Microsoft Shared\ClickToRun\OfficeC2RClient.exe" (
    set "C2R_CLIENT=%ProgramFiles(x86)%\Common Files\Microsoft Shared\ClickToRun\OfficeC2RClient.exe"
)

if not "%C2R_CLIENT%"=="" (
    echo [INFO] Starte Office Click-to-Run Update...
    "%C2R_CLIENT%" /update user
    
    echo [INFO] Warte auf Update-Abschluss...
    timeout /t 30 /nobreak >nul
    
    echo [SUCCESS] Office Click-to-Run Update gestartet!
) else (
    echo [INFO] Click-to-Run Client nicht gefunden. Versuche alternative Methoden...
)

:: Alternative: Office über Windows Update
echo [INFO] Pruefe Windows Update fuer Office Updates...
powershell -Command "& {Get-WindowsUpdate -MicrosoftUpdate | Where-Object {$_.Title -like '*Office*'} | Install-WindowsUpdate -AcceptAll -AutoReboot:$false}" 2>nul

:: Alternative: Winget für Office Updates
echo [INFO] Versuche Office Update ueber Winget...
winget upgrade --id Microsoft.Office --silent --accept-package-agreements --accept-source-agreements 2>nul

:: Office Apps einzeln über Winget updaten
echo [INFO] Update Office Apps einzeln...
winget upgrade --id Microsoft.Office.Word --silent --accept-package-agreements --accept-source-agreements 2>nul
winget upgrade --id Microsoft.Office.Excel --silent --accept-package-agreements --accept-source-agreements 2>nul
winget upgrade --id Microsoft.Office.PowerPoint --silent --accept-package-agreements --accept-source-agreements 2>nul
winget upgrade --id Microsoft.Office.Outlook --silent --accept-package-agreements --accept-source-agreements 2>nul

:: Office Cache bereinigen
echo [INFO] Bereinige Office Cache...

:: Office Apps beenden
echo [INFO] Beende Office Anwendungen...
taskkill /f /im WINWORD.EXE >nul 2>&1
taskkill /f /im EXCEL.EXE >nul 2>&1
taskkill /f /im POWERPNT.EXE >nul 2>&1
taskkill /f /im OUTLOOK.EXE >nul 2>&1
taskkill /f /im ONENOTE.EXE >nul 2>&1

:: Office Cache-Verzeichnisse bereinigen
if exist "%LOCALAPPDATA%\Microsoft\Office\16.0\OfficeFileCache" (
    rmdir /s /q "%LOCALAPPDATA%\Microsoft\Office\16.0\OfficeFileCache" >nul 2>&1
    echo [INFO] Office File Cache bereinigt.
)

if exist "%TEMP%\Word*" (
    del /f /q "%TEMP%\Word*" >nul 2>&1
)

if exist "%TEMP%\Excel*" (
    del /f /q "%TEMP%\Excel*" >nul 2>&1
)

if exist "%TEMP%\PowerPoint*" (
    del /f /q "%TEMP%\PowerPoint*" >nul 2>&1
)

echo [INFO] Office temporaere Dateien bereinigt.

:END
echo.
echo [INFO] Office Update abgeschlossen!
echo [INFO] Hinweis: Einige Office Updates erfordern einen Neustart der Office-Anwendungen.
echo.

exit /b 0
