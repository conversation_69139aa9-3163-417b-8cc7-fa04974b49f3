# Installation Guide - Advanced Admin Script

## Schritt-für-Schritt Installation

### 1. Vorbereitung

**Voraussetzungen:**
- FiveM Server (empfohlen: neueste Version)
- Grundkenntnisse in FiveM Server-Administration
- Texteditor (z.B. Visual Studio Code, Notepad++)

### 2. Download und Platzierung

1. **Script herunterladen** oder kopieren
2. **Ordner erstellen**: <PERSON><PERSON><PERSON> einen Ordner namens `advanced_admin` in deinem `resources` Verzeichnis
3. **Dateien kopieren**: Kopiere alle Script-Dateien in den erstellten Ordner

**Ordnerstruktur sollte so aussehen:**
```
resources/
└── advanced_admin/
    ├── fxmanifest.lua
    ├── config.lua
    ├── bans.json
    ├── README.md
    ├── server/
    │   ├── main.lua
    │   ├── commands.lua
    │   ├── permissions.lua
    │   ├── logging.lua
    │   └── database.lua
    ├── client/
    │   ├── main.lua
    │   ├── commands.lua
    │   ├── noclip.lua
    │   └── ui.lua
    └── html/
        ├── index.html
        ├── style.css
        └── script.js
```

### 3. Server-Konfiguration

**server.cfg bearbeiten:**
```cfg
# Advanced Admin Script
ensure advanced_admin
```

**Wichtig:** Füge diese Zeile zu deiner `server.cfg` hinzu, am besten nach anderen wichtigen Resources.

### 4. Admin-Konfiguration

**config.lua bearbeiten:**

1. **Öffne** `config.lua` in einem Texteditor
2. **Finde** den Abschnitt `Config.Admins`
3. **Füge deine Admin-Identifiers hinzu:**

```lua
Config.Admins = {
    ['steam:110000100000000'] = 'superadmin',  -- Deine Steam ID
    ['license:abcdef1234567890'] = 'admin',    -- Oder License ID
}
```

**So findest du deine Identifier:**
- **Steam ID**: Verwende ein Online-Tool oder den Befehl `/me` im Spiel
- **License ID**: Schau in die Server-Konsole wenn du dich verbindest

### 5. Erweiterte Konfiguration (Optional)

**Sprache ändern:**
```lua
Config.Locale = 'de' -- oder 'en' für Englisch
```

**Logging anpassen:**
```lua
Config.EnableLogging = true
Config.LogToFile = true
Config.LogToDatabase = false -- Nur wenn MySQL verfügbar
```

**UI-Position ändern:**
```lua
Config.UI = {
    position = 'top-right', -- top-left, top-right, bottom-left, bottom-right
    theme = 'dark',         -- dark, light
}
```

### 6. Datenbank-Setup (Optional)

**Nur wenn du erweiterte Features möchtest:**

1. **mysql-async installieren** (falls nicht vorhanden)
2. **Config anpassen:**
```lua
Config.LogToDatabase = true
```
3. **Tabellen werden automatisch erstellt** beim ersten Start

### 7. Berechtigungen anpassen

**Standard-Rollen:**
- `superadmin` (Level 100): Alle Befehle
- `admin` (Level 80): Die meisten Befehle
- `moderator` (Level 60): Basis-Moderation
- `helper` (Level 40): Grundlegende Hilfe
- `vip` (Level 20): Spezielle Rechte
- `user` (Level 0): Keine Admin-Rechte

**Eigene Rollen hinzufügen:**
```lua
Config.PermissionLevels = {
    ['owner'] = 150,        -- Neue Rolle
    ['superadmin'] = 100,
    -- ... bestehende Rollen
}
```

### 8. Server starten

1. **Server starten** oder **Resource neu laden**
2. **Konsole überprüfen** auf Fehlermeldungen
3. **Im Spiel testen** mit `/admin`

### 9. Erste Schritte

**Nach der Installation:**

1. **Verbinde dich** mit deinem Server
2. **Öffne das Admin-Menü** mit `/admin` oder `F6`
3. **Teste grundlegende Befehle** wie `/heal` oder `/tp`
4. **Überprüfe Logs** in `logs/admin_actions.log`

### 10. Troubleshooting

**Häufige Probleme:**

**Problem:** Admin-Menü öffnet nicht
**Lösung:** 
- Überprüfe deine Identifier in der Config
- Stelle sicher, dass du die richtige Berechtigung hast
- Schau in die F8-Konsole nach Fehlern

**Problem:** Befehle funktionieren nicht
**Lösung:**
- Überprüfe Server-Konsole auf Fehler
- Stelle sicher, dass das Script gestartet ist (`ensure advanced_admin`)
- Überprüfe Berechtigungen

**Problem:** UI sieht kaputt aus
**Lösung:**
- Überprüfe, ob alle HTML/CSS/JS Dateien vorhanden sind
- Leere Browser-Cache (Strg+F5)
- Überprüfe Konsole auf JavaScript-Fehler

### 11. Sicherheitshinweise

**Wichtige Sicherheitsmaßnahmen:**

1. **Sichere Identifier**: Verwende nur vertrauenswürdige Steam/License IDs
2. **Regelmäßige Updates**: Halte das Script aktuell
3. **Log-Überwachung**: Überprüfe regelmäßig die Admin-Logs
4. **Backup**: Sichere deine Konfiguration und Bans

### 12. Performance-Optimierung

**Für bessere Performance:**

1. **Log-Rotation aktivieren**: Verhindert zu große Log-Dateien
2. **Datenbank verwenden**: Für große Server empfohlen
3. **Unnötige Features deaktivieren**: In der Config

### 13. Anpassungen

**Häufige Anpassungen:**

**Neue Teleport-Orte hinzufügen:**
```lua
Config.TeleportLocations = {
    ['spawn'] = { x = -1037.0, y = -2737.0, z = 20.0, heading = 0.0 },
    ['meinort'] = { x = 100.0, y = 200.0, z = 30.0, heading = 90.0 },
}
```

**Befehle deaktivieren:**
```lua
Config.Commands = {
    kick = { permission = 'moderator', enabled = false }, -- Deaktiviert
}
```

### 14. Support und Updates

**Bei Problemen:**
1. Überprüfe die README.md für detaillierte Informationen
2. Schau in die Server-Konsole nach Fehlermeldungen
3. Teste mit einem frischen Server-Neustart

**Updates:**
- Sichere deine `config.lua` vor Updates
- Überprüfe Changelog für breaking changes
- Teste Updates erst auf einem Test-Server

---

## Schnell-Installation (Erfahrene Nutzer)

```bash
# 1. Script in resources/advanced_admin/ kopieren
# 2. server.cfg: ensure advanced_admin
# 3. config.lua: Deine Steam/License ID hinzufügen
# 4. Server starten
# 5. /admin im Spiel testen
```

**Fertig! Dein Advanced Admin Script ist einsatzbereit.**
