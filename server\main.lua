-- Advanced Admin Script - Server Main
-- Author: Your Name
-- Version: 1.0.0

local AdminScript = {}
AdminScript.Players = {}
AdminScript.Bans = {}

-- Initialize the script
Citizen.CreateThread(function()
    print("^2[Advanced Admin]^7 Starting Advanced Admin Script...")
    LoadBans()
    print("^2[Advanced Admin]^7 Script loaded successfully!")
end)

-- Player connecting event
AddEventHandler('playerConnecting', function(name, setKickReason, deferrals)
    local src = source
    local identifiers = GetPlayerIdentifiers(src)
    
    -- Check if player is banned
    for _, identifier in pairs(identifiers) do
        if AdminScript.Bans[identifier] then
            local ban = AdminScript.Bans[identifier]
            if ban.expires == 0 or ban.expires > os.time() then
                local reason = ban.reason or "Kein Grund angegeben"
                local expires = ban.expires == 0 and "Permanent" or os.date("%d.%m.%Y %H:%M", ban.expires)
                setKickReason(string.format("Du bist gebannt!\nGrund: %s\nBis: %s\nBan ID: %s", reason, expires, ban.id))
                CancelEvent()
                return
            else
                -- Ban expired, remove it
                AdminScript.Bans[identifier] = nil
                SaveBans()
            end
        end
    end
end)

-- Player joined event
AddEventHandler('playerJoined', function()
    local src = source
    AdminScript.Players[src] = {
        god = false,
        noclip = false,
        invisible = false,
        frozen = false
    }
end)

-- Player dropped event
AddEventHandler('playerDropped', function()
    local src = source
    AdminScript.Players[src] = nil
end)

-- Get player permission level
function GetPlayerPermissionLevel(src)
    local identifiers = GetPlayerIdentifiers(src)
    local highestLevel = 0
    
    for _, identifier in pairs(identifiers) do
        if Config.Admins[identifier] then
            local role = Config.Admins[identifier]
            local level = Config.PermissionLevels[role] or 0
            if level > highestLevel then
                highestLevel = level
            end
        end
    end
    
    return highestLevel
end

-- Check if player has permission
function HasPermission(src, requiredPermission)
    local playerLevel = GetPlayerPermissionLevel(src)
    local requiredLevel = Config.PermissionLevels[requiredPermission] or 0
    return playerLevel >= requiredLevel
end

-- Get localized message
function GetMessage(key, ...)
    local messages = Config.Messages[Config.Locale] or Config.Messages['en']
    local message = messages[key] or key
    return string.format(message, ...)
end

-- Send notification to player
function SendNotification(src, message, type)
    TriggerClientEvent('admin:notification', src, message, type or 'info')
end

-- Load bans from file
function LoadBans()
    local file = io.open(Config.Bans.banFile, 'r')
    if file then
        local content = file:read('*all')
        file:close()
        
        local success, data = pcall(json.decode, content)
        if success and data then
            AdminScript.Bans = data
            print(string.format("^2[Advanced Admin]^7 Loaded %d bans", #AdminScript.Bans))
        end
    else
        print("^3[Advanced Admin]^7 No ban file found, creating new one")
        SaveBans()
    end
end

-- Save bans to file
function SaveBans()
    local file = io.open(Config.Bans.banFile, 'w')
    if file then
        file:write(json.encode(AdminScript.Bans, {indent = true}))
        file:close()
    end
end

-- Ban player
function BanPlayer(targetId, adminId, reason, duration)
    local targetIdentifiers = GetPlayerIdentifiers(targetId)
    local adminName = GetPlayerName(adminId)
    local targetName = GetPlayerName(targetId)
    
    reason = reason or Config.Bans.defaultReason
    duration = duration or 0 -- 0 = permanent
    
    local banId = string.format("BAN_%d", os.time())
    local expires = duration > 0 and (os.time() + (duration * 24 * 60 * 60)) or 0
    
    for _, identifier in pairs(targetIdentifiers) do
        AdminScript.Bans[identifier] = {
            id = banId,
            reason = reason,
            admin = adminName,
            date = os.time(),
            expires = expires
        }
    end
    
    SaveBans()
    
    -- Log the action
    LogAction(adminId, string.format("banned %s (%s) - Reason: %s", targetName, targetId, reason))
    
    -- Kick the player
    DropPlayer(targetId, string.format("Du wurdest gebannt!\nGrund: %s\nAdmin: %s\nBan ID: %s", reason, adminName, banId))
    
    return banId
end

-- Unban player
function UnbanPlayer(identifier, adminId)
    if AdminScript.Bans[identifier] then
        AdminScript.Bans[identifier] = nil
        SaveBans()
        
        LogAction(adminId, string.format("unbanned %s", identifier))
        return true
    end
    return false
end

-- Export functions for other resources
exports('HasPermission', HasPermission)
exports('GetPlayerPermissionLevel', GetPlayerPermissionLevel)
exports('BanPlayer', BanPlayer)
exports('UnbanPlayer', UnbanPlayer)

-- Global access
_G.AdminScript = AdminScript
