-- Advanced Admin Script - UI Handler
-- Handles NUI communication and UI events

local uiOpen = false

-- Register NUI Callbacks
RegisterNUICallback('closeMenu', function(data, cb)
    SetNuiFocus(false, false)
    uiOpen = false
    cb('ok')
end)

RegisterNUICallback('executeCommand', function(data, cb)
    if data.command then
        -- Parse command and execute
        local args = {}
        for word in data.command:gmatch("%S+") do
            table.insert(args, word)
        end
        
        local command = table.remove(args, 1)
        ExecuteCommand(data.command)
    end
    cb('ok')
end)

RegisterNUICallback('getPlayers', function(data, cb)
    local players = {}
    
    for _, playerId in pairs(GetActivePlayers()) do
        local serverPlayerId = GetPlayerServerId(playerId)
        if serverPlayerId then
            table.insert(players, {
                id = serverPlayerId,
                name = GetPlayerName(playerId),
                ping = GetPlayerPing(playerId),
                coords = GetEntityCoords(GetPlayerPed(playerId))
            })
        end
    end
    
    cb(players)
end)

RegisterNUICallback('playerAction', function(data, cb)
    local action = data.action
    local playerId = data.playerId
    local reason = data.reason or ""
    
    if action == 'kick' then
        TriggerServerEvent('admin:kickPlayer', playerId, reason)
    elseif action == 'ban' then
        TriggerServerEvent('admin:banPlayer', playerId, 0, reason)
    elseif action == 'teleport' then
        TriggerServerEvent('admin:teleportToPlayer', playerId)
    elseif action == 'bring' then
        TriggerServerEvent('admin:bringPlayer', playerId)
    elseif action == 'heal' then
        TriggerServerEvent('admin:healPlayer', playerId)
    elseif action == 'spectate' then
        TriggerEvent('admin:spectatePlayer', playerId)
    elseif action == 'freeze' then
        TriggerServerEvent('admin:freezePlayer', playerId)
    end
    
    cb('ok')
end)

RegisterNUICallback('spawnVehicle', function(data, cb)
    if data.vehicle then
        TriggerEvent('admin:spawnVehicle', data.vehicle)
    end
    cb('ok')
end)

RegisterNUICallback('teleportToCoords', function(data, cb)
    if data.x and data.y and data.z then
        TriggerEvent('admin:teleport', data.x, data.y, data.z)
    end
    cb('ok')
end)

RegisterNUICallback('setWeather', function(data, cb)
    if data.weather then
        TriggerServerEvent('admin:setWeather', data.weather)
    end
    cb('ok')
end)

RegisterNUICallback('setTime', function(data, cb)
    if data.hour ~= nil then
        TriggerServerEvent('admin:setTime', data.hour, data.minute or 0)
    end
    cb('ok')
end)

RegisterNUICallback('sendAnnouncement', function(data, cb)
    if data.message then
        TriggerServerEvent('admin:sendAnnouncement', data.message)
    end
    cb('ok')
end)

RegisterNUICallback('getLogs', function(data, cb)
    TriggerServerEvent('admin:requestLogs', data.count or 20, data.search or "")
    cb('ok')
end)

-- Server Events
RegisterNetEvent('admin:receiveLogs')
AddEventHandler('admin:receiveLogs', function(logs)
    SendNUIMessage({
        type = "updateLogs",
        logs = logs
    })
end)

RegisterNetEvent('admin:receivePlayerList')
AddEventHandler('admin:receivePlayerList', function(players)
    SendNUIMessage({
        type = "updatePlayers",
        players = players
    })
end)

-- Utility functions
function OpenAdminMenu()
    if uiOpen then return end
    
    uiOpen = true
    SetNuiFocus(true, true)
    SendNUIMessage({
        type = "openMenu"
    })
    
    -- Request initial data
    TriggerServerEvent('admin:requestPlayerList')
end

function CloseAdminMenu()
    if not uiOpen then return end
    
    uiOpen = false
    SetNuiFocus(false, false)
    SendNUIMessage({
        type = "closeMenu"
    })
end

-- Export functions
exports('OpenAdminMenu', OpenAdminMenu)
exports('CloseAdminMenu', CloseAdminMenu)
exports('IsMenuOpen', function() return uiOpen end)

-- Global access
_G.OpenAdminMenu = OpenAdminMenu
_G.CloseAdminMenu = CloseAdminMenu
