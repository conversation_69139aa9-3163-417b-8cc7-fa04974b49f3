-- Advanced Admin Script - Server Commands
-- All admin commands are registered here

-- Kick Command
RegisterCommand('kick', function(source, args, rawCommand)
    if not HasPermission(source, 'moderator') then
        SendNotification(source, GetMessage('no_permission'), 'error')
        return
    end
    
    local targetId = tonumber(args[1])
    if not targetId or not GetPlayerName(targetId) then
        SendNotification(source, GetMessage('player_not_found'), 'error')
        return
    end
    
    local reason = table.concat(args, ' ', 2) or 'Kein Grund angegeben'
    local adminName = GetPlayerName(source)
    local targetName = GetPlayerName(targetId)
    
    DropPlayer(targetId, string.format(GetMessage('kicked'), reason))
    
    LogAction(source, string.format("kicked %s (%s) - Reason: %s", targetName, targetId, reason))
    SendNotification(source, string.format("Spieler %s wurde gekickt", targetName), 'success')
    
    -- Notify all admins
    for _, playerId in pairs(GetPlayers()) do
        if HasPermission(playerId, 'helper') then
            SendNotification(playerId, string.format("%s kicked %s - Reason: %s", adminName, targetName, reason), 'info')
        end
    end
end, false)

-- Ban Command
RegisterCommand('ban', function(source, args, rawCommand)
    if not HasPermission(source, 'admin') then
        SendNotification(source, GetMessage('no_permission'), 'error')
        return
    end
    
    local targetId = tonumber(args[1])
    if not targetId or not GetPlayerName(targetId) then
        SendNotification(source, GetMessage('player_not_found'), 'error')
        return
    end
    
    local duration = tonumber(args[2]) or 0 -- 0 = permanent
    local reason = table.concat(args, ' ', 3) or Config.Bans.defaultReason
    
    local banId = BanPlayer(targetId, source, reason, duration)
    local targetName = GetPlayerName(targetId)
    
    SendNotification(source, string.format("Spieler %s wurde gebannt (ID: %s)", targetName, banId), 'success')
end, false)

-- Unban Command
RegisterCommand('unban', function(source, args, rawCommand)
    if not HasPermission(source, 'admin') then
        SendNotification(source, GetMessage('no_permission'), 'error')
        return
    end
    
    local identifier = args[1]
    if not identifier then
        SendNotification(source, "Usage: /unban <identifier>", 'error')
        return
    end
    
    if UnbanPlayer(identifier, source) then
        SendNotification(source, string.format("Spieler %s wurde entbannt", identifier), 'success')
    else
        SendNotification(source, "Spieler ist nicht gebannt oder Identifier nicht gefunden", 'error')
    end
end, false)

-- Teleport Commands
RegisterCommand('tp', function(source, args, rawCommand)
    if not HasPermission(source, 'moderator') then
        SendNotification(source, GetMessage('no_permission'), 'error')
        return
    end
    
    local x, y, z = tonumber(args[1]), tonumber(args[2]), tonumber(args[3])
    if not x or not y or not z then
        SendNotification(source, "Usage: /tp <x> <y> <z>", 'error')
        return
    end
    
    TriggerClientEvent('admin:teleport', source, x, y, z)
    SendNotification(source, GetMessage('teleported'), 'success')
    LogAction(source, string.format("teleported to %s, %s, %s", x, y, z))
end, false)

RegisterCommand('tpto', function(source, args, rawCommand)
    if not HasPermission(source, 'moderator') then
        SendNotification(source, GetMessage('no_permission'), 'error')
        return
    end
    
    local targetId = tonumber(args[1])
    if not targetId or not GetPlayerName(targetId) then
        SendNotification(source, GetMessage('player_not_found'), 'error')
        return
    end
    
    TriggerClientEvent('admin:teleportToPlayer', source, targetId)
    SendNotification(source, string.format("Teleportiert zu %s", GetPlayerName(targetId)), 'success')
    LogAction(source, string.format("teleported to player %s (%s)", GetPlayerName(targetId), targetId))
end, false)

RegisterCommand('tphere', function(source, args, rawCommand)
    if not HasPermission(source, 'moderator') then
        SendNotification(source, GetMessage('no_permission'), 'error')
        return
    end
    
    local targetId = tonumber(args[1])
    if not targetId or not GetPlayerName(targetId) then
        SendNotification(source, GetMessage('player_not_found'), 'error')
        return
    end
    
    TriggerClientEvent('admin:teleportPlayerHere', targetId, source)
    SendNotification(source, string.format("%s zu dir teleportiert", GetPlayerName(targetId)), 'success')
    SendNotification(targetId, string.format("Du wurdest zu %s teleportiert", GetPlayerName(source)), 'info')
    LogAction(source, string.format("teleported player %s (%s) to self", GetPlayerName(targetId), targetId))
end, false)

-- Heal Command
RegisterCommand('heal', function(source, args, rawCommand)
    if not HasPermission(source, 'moderator') then
        SendNotification(source, GetMessage('no_permission'), 'error')
        return
    end
    
    local targetId = tonumber(args[1]) or source
    if not GetPlayerName(targetId) then
        SendNotification(source, GetMessage('player_not_found'), 'error')
        return
    end
    
    TriggerClientEvent('admin:heal', targetId)
    SendNotification(targetId, GetMessage('healed'), 'success')
    
    if targetId ~= source then
        SendNotification(source, string.format("%s wurde geheilt", GetPlayerName(targetId)), 'success')
        LogAction(source, string.format("healed player %s (%s)", GetPlayerName(targetId), targetId))
    else
        LogAction(source, "healed self")
    end
end, false)

-- Armor Command
RegisterCommand('armor', function(source, args, rawCommand)
    if not HasPermission(source, 'moderator') then
        SendNotification(source, GetMessage('no_permission'), 'error')
        return
    end
    
    local targetId = tonumber(args[1]) or source
    if not GetPlayerName(targetId) then
        SendNotification(source, GetMessage('player_not_found'), 'error')
        return
    end
    
    TriggerClientEvent('admin:armor', targetId)
    SendNotification(targetId, "Rüstung aufgefüllt", 'success')
    
    if targetId ~= source then
        SendNotification(source, string.format("%s erhielt Rüstung", GetPlayerName(targetId)), 'success')
        LogAction(source, string.format("gave armor to player %s (%s)", GetPlayerName(targetId), targetId))
    else
        LogAction(source, "gave armor to self")
    end
end, false)

-- God Mode Command
RegisterCommand('god', function(source, args, rawCommand)
    if not HasPermission(source, 'admin') then
        SendNotification(source, GetMessage('no_permission'), 'error')
        return
    end
    
    local targetId = tonumber(args[1]) or source
    if not GetPlayerName(targetId) then
        SendNotification(source, GetMessage('player_not_found'), 'error')
        return
    end
    
    AdminScript.Players[targetId] = AdminScript.Players[targetId] or {}
    AdminScript.Players[targetId].god = not AdminScript.Players[targetId].god
    
    TriggerClientEvent('admin:toggleGod', targetId, AdminScript.Players[targetId].god)
    
    local status = AdminScript.Players[targetId].god and 'aktiviert' or 'deaktiviert'
    SendNotification(targetId, string.format("God Mode %s", status), 'info')
    
    if targetId ~= source then
        SendNotification(source, string.format("God Mode für %s %s", GetPlayerName(targetId), status), 'success')
    end
    
    LogAction(source, string.format("toggled god mode for %s (%s) - %s", GetPlayerName(targetId), targetId, status))
end, false)

-- NoClip Command
RegisterCommand('noclip', function(source, args, rawCommand)
    if not HasPermission(source, 'admin') then
        SendNotification(source, GetMessage('no_permission'), 'error')
        return
    end

    AdminScript.Players[source] = AdminScript.Players[source] or {}
    AdminScript.Players[source].noclip = not AdminScript.Players[source].noclip

    TriggerClientEvent('admin:toggleNoclip', source, AdminScript.Players[source].noclip)

    local status = AdminScript.Players[source].noclip and 'aktiviert' or 'deaktiviert'
    SendNotification(source, string.format("NoClip %s", status), 'info')
    LogAction(source, string.format("toggled noclip - %s", status))
end, false)

-- Vehicle Commands
RegisterCommand('car', function(source, args, rawCommand)
    if not HasPermission(source, 'moderator') then
        SendNotification(source, GetMessage('no_permission'), 'error')
        return
    end

    local vehicleName = args[1]
    if not vehicleName then
        SendNotification(source, "Usage: /car <vehicle_name>", 'error')
        return
    end

    TriggerClientEvent('admin:spawnVehicle', source, vehicleName)
    LogAction(source, string.format("spawned vehicle %s", vehicleName))
end, false)

RegisterCommand('dv', function(source, args, rawCommand)
    if not HasPermission(source, 'moderator') then
        SendNotification(source, GetMessage('no_permission'), 'error')
        return
    end

    TriggerClientEvent('admin:deleteVehicle', source)
    SendNotification(source, "Fahrzeug gelöscht", 'success')
    LogAction(source, "deleted vehicle")
end, false)

RegisterCommand('fix', function(source, args, rawCommand)
    if not HasPermission(source, 'moderator') then
        SendNotification(source, GetMessage('no_permission'), 'error')
        return
    end

    TriggerClientEvent('admin:fixVehicle', source)
    SendNotification(source, "Fahrzeug repariert", 'success')
    LogAction(source, "fixed vehicle")
end, false)

-- Weather Command
RegisterCommand('weather', function(source, args, rawCommand)
    if not HasPermission(source, 'admin') then
        SendNotification(source, GetMessage('no_permission'), 'error')
        return
    end

    local weather = string.upper(args[1] or 'CLEAR')
    local validWeather = false

    for _, w in pairs(Config.WeatherTypes) do
        if w == weather then
            validWeather = true
            break
        end
    end

    if not validWeather then
        SendNotification(source, "Ungültiger Wettertyp. Verfügbar: " .. table.concat(Config.WeatherTypes, ', '), 'error')
        return
    end

    TriggerClientEvent('admin:setWeather', -1, weather)
    SendNotification(source, string.format(GetMessage('weather_changed'), weather), 'success')
    LogAction(source, string.format("changed weather to %s", weather))
end, false)

-- Time Command
RegisterCommand('time', function(source, args, rawCommand)
    if not HasPermission(source, 'admin') then
        SendNotification(source, GetMessage('no_permission'), 'error')
        return
    end

    local hour = tonumber(args[1])
    local minute = tonumber(args[2]) or 0

    if not hour or hour < 0 or hour > 23 or minute < 0 or minute > 59 then
        SendNotification(source, "Usage: /time <hour> [minute]", 'error')
        return
    end

    TriggerClientEvent('admin:setTime', -1, hour, minute)
    SendNotification(source, string.format(GetMessage('time_changed'), hour, minute), 'success')
    LogAction(source, string.format("changed time to %02d:%02d", hour, minute))
end, false)

-- Freeze Command
RegisterCommand('freeze', function(source, args, rawCommand)
    if not HasPermission(source, 'moderator') then
        SendNotification(source, GetMessage('no_permission'), 'error')
        return
    end

    local targetId = tonumber(args[1])
    if not targetId or not GetPlayerName(targetId) then
        SendNotification(source, GetMessage('player_not_found'), 'error')
        return
    end

    AdminScript.Players[targetId] = AdminScript.Players[targetId] or {}
    AdminScript.Players[targetId].frozen = true

    TriggerClientEvent('admin:freeze', targetId, true)
    SendNotification(source, string.format("%s wurde eingefroren", GetPlayerName(targetId)), 'success')
    SendNotification(targetId, "Du wurdest eingefroren", 'info')
    LogAction(source, string.format("froze player %s (%s)", GetPlayerName(targetId), targetId))
end, false)

RegisterCommand('unfreeze', function(source, args, rawCommand)
    if not HasPermission(source, 'moderator') then
        SendNotification(source, GetMessage('no_permission'), 'error')
        return
    end

    local targetId = tonumber(args[1])
    if not targetId or not GetPlayerName(targetId) then
        SendNotification(source, GetMessage('player_not_found'), 'error')
        return
    end

    AdminScript.Players[targetId] = AdminScript.Players[targetId] or {}
    AdminScript.Players[targetId].frozen = false

    TriggerClientEvent('admin:freeze', targetId, false)
    SendNotification(source, string.format("%s wurde aufgetaut", GetPlayerName(targetId)), 'success')
    SendNotification(targetId, "Du wurdest aufgetaut", 'info')
    LogAction(source, string.format("unfroze player %s (%s)", GetPlayerName(targetId), targetId))
end, false)

-- Admin Menu Command
RegisterCommand('admin', function(source, args, rawCommand)
    if not HasPermission(source, 'helper') then
        SendNotification(source, GetMessage('no_permission'), 'error')
        return
    end

    TriggerClientEvent('admin:openMenu', source)
end, false)

-- Announce Command
RegisterCommand('announce', function(source, args, rawCommand)
    if not HasPermission(source, 'moderator') then
        SendNotification(source, GetMessage('no_permission'), 'error')
        return
    end

    local message = table.concat(args, ' ')
    if not message or message == '' then
        SendNotification(source, "Usage: /announce <message>", 'error')
        return
    end

    local adminName = GetPlayerName(source)
    TriggerClientEvent('chat:addMessage', -1, {
        color = { 255, 0, 0 },
        multiline = true,
        args = { "[ANKÜNDIGUNG]", message }
    })

    LogAction(source, string.format("sent announcement: %s", message))
    SendNotification(source, "Ankündigung gesendet", 'success')
end, false)

-- Revive Command
RegisterCommand('revive', function(source, args, rawCommand)
    if not HasPermission(source, 'moderator') then
        SendNotification(source, GetMessage('no_permission'), 'error')
        return
    end

    local targetId = tonumber(args[1]) or source
    if not GetPlayerName(targetId) then
        SendNotification(source, GetMessage('player_not_found'), 'error')
        return
    end

    TriggerClientEvent('admin:revive', targetId)
    SendNotification(targetId, "Du wurdest wiederbelebt", 'success')

    if targetId ~= source then
        SendNotification(source, string.format("%s wurde wiederbelebt", GetPlayerName(targetId)), 'success')
        LogAction(source, string.format("revived player %s (%s)", GetPlayerName(targetId), targetId))
    else
        LogAction(source, "revived self")
    end
end, false)

-- Invisible Command
RegisterCommand('invisible', function(source, args, rawCommand)
    if not HasPermission(source, 'admin') then
        SendNotification(source, GetMessage('no_permission'), 'error')
        return
    end

    AdminScript.Players[source] = AdminScript.Players[source] or {}
    AdminScript.Players[source].invisible = not AdminScript.Players[source].invisible

    TriggerClientEvent('admin:toggleInvisible', source, AdminScript.Players[source].invisible)

    local status = AdminScript.Players[source].invisible and 'aktiviert' or 'deaktiviert'
    SendNotification(source, string.format("Unsichtbarkeit %s", status), 'info')
    LogAction(source, string.format("toggled invisibility - %s", status))
end, false)
