@echo off
:: ====================================================================
:: Adobe Reader Update Script
:: Aktualisiert Adobe Acrobat Reader auf die neueste Version
:: ====================================================================

echo.
echo ====================================================================
echo                        ADOBE READER UPDATE
echo ====================================================================
echo.

:: Adobe Reader Installation prüfen
set "ADOBE_FOUND=0"
set "ADOBE_PATH="

:: Adobe Reader DC (64-bit) prüfen
if exist "%ProgramFiles%\Adobe\Acrobat DC\Acrobat\Acrobat.exe" (
    set "ADOBE_PATH=%ProgramFiles%\Adobe\Acrobat DC\Acrobat"
    set "ADOBE_FOUND=1"
    echo [INFO] Adobe Acrobat DC gefunden: %ADOBE_PATH%
) else if exist "%ProgramFiles%\Adobe\Acrobat Reader DC\Reader\AcroRd32.exe" (
    set "ADOBE_PATH=%ProgramFiles%\Adobe\Acrobat Reader DC\Reader"
    set "ADOBE_FOUND=1"
    echo [INFO] Adobe Reader DC (64-bit) gefunden: %ADOBE_PATH%
)

:: Adobe Reader DC (32-bit) prüfen
if %ADOBE_FOUND% equ 0 (
    if exist "%ProgramFiles(x86)%\Adobe\Acrobat Reader DC\Reader\AcroRd32.exe" (
        set "ADOBE_PATH=%ProgramFiles(x86)%\Adobe\Acrobat Reader DC\Reader"
        set "ADOBE_FOUND=1"
        echo [INFO] Adobe Reader DC (32-bit) gefunden: %ADOBE_PATH%
    )
)

if %ADOBE_FOUND% equ 0 (
    echo [INFO] Adobe Reader ist nicht installiert.
    echo [INFO] Installiere Adobe Reader ueber Winget...
    winget install --id Adobe.Acrobat.Reader.64-bit --silent --accept-package-agreements --accept-source-agreements
    goto END
)

:: Adobe Reader Version anzeigen
echo [INFO] Aktuelle Adobe Reader Version:
if exist "%ADOBE_PATH%\AcroRd32.exe" (
    powershell -Command "& {(Get-ItemProperty '%ADOBE_PATH%\AcroRd32.exe').VersionInfo.FileVersion}"
) else if exist "%ADOBE_PATH%\Acrobat.exe" (
    powershell -Command "& {(Get-ItemProperty '%ADOBE_PATH%\Acrobat.exe').VersionInfo.FileVersion}"
)
echo.

:: Adobe Reader über Winget updaten (bevorzugte Methode)
echo [INFO] Versuche Adobe Reader Update ueber Winget...
winget upgrade --id Adobe.Acrobat.Reader.64-bit --silent --accept-package-agreements --accept-source-agreements
if %errorLevel% equ 0 (
    echo [SUCCESS] Adobe Reader erfolgreich ueber Winget aktualisiert!
    goto CLEANUP
)

:: Alternative: Adobe Reader 32-bit über Winget
winget upgrade --id Adobe.Acrobat.Reader.32-bit --silent --accept-package-agreements --accept-source-agreements
if %errorLevel% equ 0 (
    echo [SUCCESS] Adobe Reader (32-bit) erfolgreich ueber Winget aktualisiert!
    goto CLEANUP
)

:: Alternative: Direkter Download und Installation
echo [INFO] Winget Update fehlgeschlagen. Versuche direkten Download...

:: Temporäres Verzeichnis erstellen
set "TEMP_DIR=%TEMP%\AdobeUpdate"
if not exist "%TEMP_DIR%" mkdir "%TEMP_DIR%"

:: Adobe Reader Installer herunterladen
echo [INFO] Lade Adobe Reader Installer herunter...
powershell -Command "& {Invoke-WebRequest -Uri 'https://get.adobe.com/reader/download/?installer=Reader_DC_2023.008.20470_German_Windows(64Bit)&standalone=1' -OutFile '%TEMP_DIR%\AdobeReader-Setup.exe'}" 2>nul

if not exist "%TEMP_DIR%\AdobeReader-Setup.exe" (
    echo [INFO] Versuche alternativen Download-Link...
    powershell -Command "& {Invoke-WebRequest -Uri 'https://ardownload2.adobe.com/pub/adobe/reader/win/AcrobatDC/2300820470/AcroRdrDC2300820470_de_DE.exe' -OutFile '%TEMP_DIR%\AdobeReader-Setup.exe'}" 2>nul
)

if exist "%TEMP_DIR%\AdobeReader-Setup.exe" (
    echo [INFO] Installiere Adobe Reader...
    "%TEMP_DIR%\AdobeReader-Setup.exe" /sAll /rs /msi EULA_ACCEPT=YES
    
    if %errorLevel% equ 0 (
        echo [SUCCESS] Adobe Reader erfolgreich installiert/aktualisiert!
    ) else (
        echo [FEHLER] Adobe Reader Installation fehlgeschlagen!
    )
) else (
    echo [FEHLER] Adobe Reader Download fehlgeschlagen!
)

:CLEANUP
echo [INFO] Bereinige Adobe Reader Cache und temporaere Dateien...

:: Adobe Reader beenden falls läuft
taskkill /f /im AcroRd32.exe >nul 2>&1
taskkill /f /im Acrobat.exe >nul 2>&1

:: Adobe Cache bereinigen
if exist "%LOCALAPPDATA%\Adobe\Acrobat\DC\Cache" (
    rmdir /s /q "%LOCALAPPDATA%\Adobe\Acrobat\DC\Cache" >nul 2>&1
    echo [INFO] Adobe Acrobat Cache bereinigt.
)

if exist "%APPDATA%\Adobe\Acrobat\DC\Security" (
    del /f /q "%APPDATA%\Adobe\Acrobat\DC\Security\*.*" >nul 2>&1
)

:: Temporäre Adobe Dateien bereinigen
if exist "%TEMP%\Adobe*" (
    rmdir /s /q "%TEMP%\Adobe*" >nul 2>&1
)

:: Update-Verzeichnis bereinigen
if exist "%TEMP_DIR%" rmdir /s /q "%TEMP_DIR%"

echo [INFO] Adobe Reader Cache bereinigt.

:END
echo.
echo [INFO] Adobe Reader Update abgeschlossen!
echo.

:: Adobe Reader Updater aktivieren
echo [INFO] Aktiviere automatische Adobe Reader Updates...
reg add "HKLM\SOFTWARE\Adobe\Adobe ARM\1.0\ARM" /v iCheck /t REG_DWORD /d 1 /f >nul 2>&1
reg add "HKLM\SOFTWARE\Adobe\Adobe ARM\1.0\ARM" /v iCheckReader /t REG_DWORD /d 1 /f >nul 2>&1

echo [INFO] Automatische Adobe Updates aktiviert.
echo.

exit /b 0
