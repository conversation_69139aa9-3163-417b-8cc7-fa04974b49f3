<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Admin Menu</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Notification Container -->
    <div id="notifications"></div>

    <!-- Admin Menu -->
    <div id="adminMenu" class="admin-menu hidden">
        <div class="menu-header">
            <h2><i class="fas fa-shield-alt"></i> Advanced Admin</h2>
            <button id="closeMenu" class="close-btn">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="menu-content">
            <!-- Navigation Tabs -->
            <div class="nav-tabs">
                <button class="tab-btn active" data-tab="players">
                    <i class="fas fa-users"></i> Spieler
                </button>
                <button class="tab-btn" data-tab="server">
                    <i class="fas fa-server"></i> Server
                </button>
                <button class="tab-btn" data-tab="vehicles">
                    <i class="fas fa-car"></i> Fahrzeuge
                </button>
                <button class="tab-btn" data-tab="teleport">
                    <i class="fas fa-location-arrow"></i> Teleport
                </button>
                <button class="tab-btn" data-tab="logs">
                    <i class="fas fa-file-alt"></i> Logs
                </button>
            </div>

            <!-- Players Tab -->
            <div id="players" class="tab-content active">
                <div class="search-bar">
                    <input type="text" id="playerSearch" placeholder="Spieler suchen...">
                    <button id="refreshPlayers"><i class="fas fa-sync"></i></button>
                </div>
                
                <div class="player-list" id="playerList">
                    <!-- Players will be populated here -->
                </div>
            </div>

            <!-- Server Tab -->
            <div id="server" class="tab-content">
                <div class="server-controls">
                    <div class="control-group">
                        <h3><i class="fas fa-cloud"></i> Wetter</h3>
                        <select id="weatherSelect">
                            <option value="CLEAR">Klar</option>
                            <option value="EXTRASUNNY">Extra Sonnig</option>
                            <option value="CLOUDS">Bewölkt</option>
                            <option value="OVERCAST">Bedeckt</option>
                            <option value="RAIN">Regen</option>
                            <option value="CLEARING">Aufklarend</option>
                            <option value="THUNDER">Gewitter</option>
                            <option value="SMOG">Smog</option>
                            <option value="FOGGY">Neblig</option>
                            <option value="SNOWLIGHT">Leichter Schnee</option>
                            <option value="BLIZZARD">Schneesturm</option>
                        </select>
                        <button id="setWeather">Setzen</button>
                    </div>

                    <div class="control-group">
                        <h3><i class="fas fa-clock"></i> Zeit</h3>
                        <input type="number" id="timeHour" min="0" max="23" placeholder="Stunde">
                        <input type="number" id="timeMinute" min="0" max="59" placeholder="Minute">
                        <button id="setTime">Setzen</button>
                    </div>

                    <div class="control-group">
                        <h3><i class="fas fa-bullhorn"></i> Ankündigung</h3>
                        <textarea id="announceText" placeholder="Nachricht eingeben..."></textarea>
                        <button id="sendAnnounce">Senden</button>
                    </div>
                </div>
            </div>

            <!-- Vehicles Tab -->
            <div id="vehicles" class="tab-content">
                <div class="vehicle-controls">
                    <div class="control-group">
                        <h3><i class="fas fa-plus"></i> Fahrzeug Spawnen</h3>
                        <input type="text" id="vehicleName" placeholder="Fahrzeugname (z.B. adder)">
                        <button id="spawnVehicle">Normal Spawnen</button>
                        <button id="spawnSupercar" class="btn-supercar">Supercar Spawnen</button>
                    </div>

                    <div class="control-group">
                        <h3><i class="fas fa-wrench"></i> Fahrzeug Aktionen</h3>
                        <button id="fixVehicle">Reparieren</button>
                        <button id="deleteVehicle">Löschen</button>
                    </div>

                    <div class="control-group">
                        <h3><i class="fas fa-list"></i> Beliebte Fahrzeuge</h3>
                        <div class="vehicle-grid">
                            <button class="vehicle-btn" data-vehicle="adder">Adder</button>
                            <button class="vehicle-btn" data-vehicle="zentorno">Zentorno</button>
                            <button class="vehicle-btn" data-vehicle="t20">T20</button>
                            <button class="vehicle-btn" data-vehicle="insurgent">Insurgent</button>
                            <button class="vehicle-btn" data-vehicle="buzzard">Buzzard</button>
                            <button class="vehicle-btn" data-vehicle="hydra">Hydra</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Teleport Tab -->
            <div id="teleport" class="tab-content">
                <div class="teleport-controls">
                    <div class="control-group">
                        <h3><i class="fas fa-crosshairs"></i> Koordinaten</h3>
                        <input type="number" id="tpX" placeholder="X" step="0.1">
                        <input type="number" id="tpY" placeholder="Y" step="0.1">
                        <input type="number" id="tpZ" placeholder="Z" step="0.1">
                        <button id="teleportCoords">Teleportieren</button>
                    </div>

                    <div class="control-group">
                        <h3><i class="fas fa-map-marker"></i> Vordefinierte Orte</h3>
                        <div class="location-grid">
                            <button class="location-btn" data-location="spawn">Spawn</button>
                            <button class="location-btn" data-location="airport">Flughafen</button>
                            <button class="location-btn" data-location="hospital">Krankenhaus</button>
                            <button class="location-btn" data-location="police">Polizei</button>
                        </div>
                    </div>

                    <div class="control-group">
                        <h3><i class="fas fa-route"></i> Waypoint</h3>
                        <button id="tpWaypoint">Zu Waypoint</button>
                        <button id="getCurrentCoords">Aktuelle Koordinaten</button>
                    </div>
                </div>
            </div>

            <!-- Logs Tab -->
            <div id="logs" class="tab-content">
                <div class="logs-controls">
                    <div class="control-group">
                        <h3><i class="fas fa-search"></i> Log Suche</h3>
                        <input type="text" id="logSearch" placeholder="Suche in Logs...">
                        <button id="searchLogs">Suchen</button>
                        <button id="refreshLogs">Aktualisieren</button>
                    </div>
                    
                    <div class="logs-container" id="logsContainer">
                        <!-- Logs will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Player Context Menu -->
    <div id="playerContextMenu" class="context-menu hidden">
        <div class="context-item" data-action="kick">
            <i class="fas fa-sign-out-alt"></i> Kicken
        </div>
        <div class="context-item" data-action="ban">
            <i class="fas fa-ban"></i> Bannen
        </div>
        <div class="context-item" data-action="teleport">
            <i class="fas fa-location-arrow"></i> Teleportieren zu
        </div>
        <div class="context-item" data-action="bring">
            <i class="fas fa-hand-paper"></i> Herbringen
        </div>
        <div class="context-item" data-action="heal">
            <i class="fas fa-heart"></i> Heilen
        </div>
        <div class="context-item" data-action="spectate">
            <i class="fas fa-eye"></i> Spectaten
        </div>
        <div class="context-item" data-action="freeze">
            <i class="fas fa-snowflake"></i> Einfrieren
        </div>
    </div>

    <!-- Modal for input dialogs -->
    <div id="modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">Eingabe</h3>
                <button id="modalClose" class="close-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <input type="text" id="modalInput" placeholder="Eingabe...">
                <textarea id="modalTextarea" placeholder="Eingabe..." class="hidden"></textarea>
            </div>
            <div class="modal-footer">
                <button id="modalCancel" class="btn-secondary">Abbrechen</button>
                <button id="modalConfirm" class="btn-primary">Bestätigen</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
