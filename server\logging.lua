-- Advanced Admin Script - Logging System
-- Handles all logging functionality

local logBuffer = {}
local logFile = nil

-- Initialize logging system
Citizen.CreateThread(function()
    if Config.EnableLogging and Config.LogToFile then
        -- Create logs directory if it doesn't exist
        os.execute('mkdir logs 2>nul') -- Windows
        
        -- Open log file
        logFile = io.open(Config.Logging.logFile, 'a')
        if logFile then
            logFile:write(string.format("\n=== Server Started: %s ===\n", os.date("%Y-%m-%d %H:%M:%S")))
            logFile:flush()
            print("^2[Advanced Admin]^7 Logging system initialized")
        else
            print("^1[Advanced Admin]^7 Failed to open log file")
        end
    end
end)

-- Log an admin action
function LogAction(playerId, action)
    if not Config.EnableLogging then
        return
    end
    
    local playerName = GetPlayerName(playerId) or "Console"
    local timestamp = os.date("%Y-%m-%d %H:%M:%S")
    local logEntry = string.format(Config.Logging.logFormat, timestamp, playerName, action)
    
    -- Print to console
    print(string.format("^3[Admin Log]^7 %s", logEntry))
    
    -- Log to file
    if Config.LogToFile and logFile then
        logFile:write(logEntry .. "\n")
        logFile:flush()
        
        -- Check file size and rotate if necessary
        CheckLogRotation()
    end
    
    -- Log to database
    if Config.LogToDatabase then
        LogToDatabase(playerId, playerName, action, timestamp)
    end
    
    -- Add to buffer for recent logs
    table.insert(logBuffer, {
        timestamp = timestamp,
        player = playerName,
        action = action,
        playerId = playerId
    })
    
    -- Keep only last 100 entries in buffer
    if #logBuffer > 100 then
        table.remove(logBuffer, 1)
    end
end

-- Check if log file needs rotation
function CheckLogRotation()
    if not logFile then return end
    
    local currentPos = logFile:seek()
    if currentPos and currentPos > Config.Logging.maxLogSize then
        RotateLogFile()
    end
end

-- Rotate log file
function RotateLogFile()
    if logFile then
        logFile:close()
    end
    
    local timestamp = os.date("%Y%m%d_%H%M%S")
    local backupFile = string.format("logs/admin_actions_%s.log", timestamp)
    
    -- Rename current log file
    os.rename(Config.Logging.logFile, backupFile)
    
    -- Create new log file
    logFile = io.open(Config.Logging.logFile, 'w')
    if logFile then
        logFile:write(string.format("=== Log Rotated: %s ===\n", os.date("%Y-%m-%d %H:%M:%S")))
        logFile:flush()
        print(string.format("^3[Advanced Admin]^7 Log file rotated to %s", backupFile))
    end
end

-- Log to database (requires mysql-async)
function LogToDatabase(playerId, playerName, action, timestamp)
    if not MySQL then
        return
    end
    
    local identifiers = GetPlayerIdentifiers(playerId)
    local steamId = ""
    local license = ""
    
    for _, identifier in pairs(identifiers) do
        if string.find(identifier, "steam:") then
            steamId = identifier
        elseif string.find(identifier, "license:") then
            license = identifier
        end
    end
    
    MySQL.Async.execute('INSERT INTO admin_logs (player_id, player_name, steam_id, license, action, timestamp) VALUES (@player_id, @player_name, @steam_id, @license, @action, @timestamp)', {
        ['@player_id'] = playerId,
        ['@player_name'] = playerName,
        ['@steam_id'] = steamId,
        ['@license'] = license,
        ['@action'] = action,
        ['@timestamp'] = timestamp
    })
end

-- Get recent logs
function GetRecentLogs(count)
    count = count or 50
    local recentLogs = {}
    
    local startIndex = math.max(1, #logBuffer - count + 1)
    for i = startIndex, #logBuffer do
        table.insert(recentLogs, logBuffer[i])
    end
    
    return recentLogs
end

-- Search logs
function SearchLogs(searchTerm, count)
    count = count or 50
    local matchingLogs = {}
    
    for i = #logBuffer, 1, -1 do
        local log = logBuffer[i]
        if string.find(string.lower(log.player), string.lower(searchTerm)) or 
           string.find(string.lower(log.action), string.lower(searchTerm)) then
            table.insert(matchingLogs, log)
            if #matchingLogs >= count then
                break
            end
        end
    end
    
    return matchingLogs
end

-- Commands for log management
RegisterCommand('logs', function(source, args, rawCommand)
    if not HasPermission(source, 'admin') then
        SendNotification(source, GetMessage('no_permission'), 'error')
        return
    end
    
    local count = tonumber(args[1]) or 10
    count = math.min(count, 50) -- Limit to 50 entries
    
    local logs = GetRecentLogs(count)
    
    if #logs == 0 then
        SendNotification(source, "Keine Logs verfügbar", 'info')
        return
    end
    
    SendNotification(source, string.format("^2Letzte %d Admin-Aktionen:", #logs), 'info')
    for _, log in pairs(logs) do
        SendNotification(source, string.format("^7[%s] %s: %s", log.timestamp, log.player, log.action), 'info')
    end
end, false)

RegisterCommand('searchlogs', function(source, args, rawCommand)
    if not HasPermission(source, 'admin') then
        SendNotification(source, GetMessage('no_permission'), 'error')
        return
    end
    
    local searchTerm = args[1]
    if not searchTerm then
        SendNotification(source, "Usage: /searchlogs <search_term>", 'error')
        return
    end
    
    local logs = SearchLogs(searchTerm, 20)
    
    if #logs == 0 then
        SendNotification(source, string.format("Keine Logs gefunden für: %s", searchTerm), 'info')
        return
    end
    
    SendNotification(source, string.format("^2Suchergebnisse für '%s' (%d gefunden):", searchTerm, #logs), 'info')
    for _, log in pairs(logs) do
        SendNotification(source, string.format("^7[%s] %s: %s", log.timestamp, log.player, log.action), 'info')
    end
end, false)

-- Clean up on resource stop
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        if logFile then
            logFile:write(string.format("=== Server Stopped: %s ===\n", os.date("%Y-%m-%d %H:%M:%S")))
            logFile:close()
        end
    end
end)

-- Export functions
exports('LogAction', LogAction)
exports('GetRecentLogs', GetRecentLogs)
exports('SearchLogs', SearchLogs)

-- Global access
_G.LogAction = LogAction
