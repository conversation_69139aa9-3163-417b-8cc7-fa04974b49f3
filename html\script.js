// Advanced Admin Menu JavaScript
class AdminMenu {
    constructor() {
        this.isOpen = false;
        this.currentTab = 'players';
        this.selectedPlayer = null;
        this.players = [];
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.setupTabs();
        this.loadPlayers();
    }

    bindEvents() {
        // Close menu
        document.getElementById('closeMenu').addEventListener('click', () => {
            this.closeMenu();
        });

        // Tab switching
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // Server controls
        document.getElementById('setWeather').addEventListener('click', () => {
            const weather = document.getElementById('weatherSelect').value;
            this.executeCommand(`weather ${weather}`);
        });

        document.getElementById('setTime').addEventListener('click', () => {
            const hour = document.getElementById('timeHour').value;
            const minute = document.getElementById('timeMinute').value || 0;
            if (hour !== '') {
                this.executeCommand(`time ${hour} ${minute}`);
            }
        });

        document.getElementById('sendAnnounce').addEventListener('click', () => {
            const message = document.getElementById('announceText').value;
            if (message.trim()) {
                this.executeCommand(`announce ${message}`);
                document.getElementById('announceText').value = '';
            }
        });

        // Vehicle controls
        document.getElementById('spawnVehicle').addEventListener('click', () => {
            const vehicle = document.getElementById('vehicleName').value;
            if (vehicle.trim()) {
                this.executeCommand(`car ${vehicle}`);
                document.getElementById('vehicleName').value = '';
            }
        });

        document.getElementById('fixVehicle').addEventListener('click', () => {
            this.executeCommand('fix');
        });

        document.getElementById('deleteVehicle').addEventListener('click', () => {
            this.executeCommand('dv');
        });

        // Vehicle grid buttons
        document.querySelectorAll('.vehicle-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const vehicle = e.target.dataset.vehicle;
                this.executeCommand(`car ${vehicle}`);
            });
        });

        // Teleport controls
        document.getElementById('teleportCoords').addEventListener('click', () => {
            const x = document.getElementById('tpX').value;
            const y = document.getElementById('tpY').value;
            const z = document.getElementById('tpZ').value;
            if (x && y && z) {
                this.executeCommand(`tp ${x} ${y} ${z}`);
            }
        });

        document.getElementById('tpWaypoint').addEventListener('click', () => {
            this.executeCommand('tpwp');
        });

        document.getElementById('getCurrentCoords').addEventListener('click', () => {
            this.executeCommand('coords');
        });

        // Location grid buttons
        document.querySelectorAll('.location-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const location = e.target.dataset.location;
                this.executeCommand(`tploc ${location}`);
            });
        });

        // Player search
        document.getElementById('playerSearch').addEventListener('input', (e) => {
            this.filterPlayers(e.target.value);
        });

        document.getElementById('refreshPlayers').addEventListener('click', () => {
            this.loadPlayers();
        });

        // Logs
        document.getElementById('searchLogs').addEventListener('click', () => {
            const searchTerm = document.getElementById('logSearch').value;
            if (searchTerm.trim()) {
                this.executeCommand(`searchlogs ${searchTerm}`);
            }
        });

        document.getElementById('refreshLogs').addEventListener('click', () => {
            this.executeCommand('logs 20');
        });

        // Modal events
        document.getElementById('modalClose').addEventListener('click', () => {
            this.hideModal();
        });

        document.getElementById('modalCancel').addEventListener('click', () => {
            this.hideModal();
        });

        // Context menu
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.context-menu')) {
                this.hideContextMenu();
            }
        });

        // ESC key to close menu
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                if (this.isModalOpen()) {
                    this.hideModal();
                } else if (this.isOpen) {
                    this.closeMenu();
                }
            }
        });
    }

    setupTabs() {
        document.querySelectorAll('.tab-content').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        document.getElementById(this.currentTab).classList.add('active');
        document.querySelector(`[data-tab="${this.currentTab}"]`).classList.add('active');
    }

    switchTab(tabName) {
        this.currentTab = tabName;
        this.setupTabs();

        // Load tab-specific data
        switch (tabName) {
            case 'players':
                this.loadPlayers();
                break;
            case 'logs':
                this.executeCommand('logs 20');
                break;
        }
    }

    openMenu() {
        this.isOpen = true;
        document.getElementById('adminMenu').classList.remove('hidden');
        this.loadPlayers();
    }

    closeMenu() {
        this.isOpen = false;
        document.getElementById('adminMenu').classList.add('hidden');
        this.hideContextMenu();
        this.hideModal();
        
        // Notify FiveM
        fetch(`https://${GetParentResourceName()}/closeMenu`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({})
        });
    }

    executeCommand(command) {
        fetch(`https://${GetParentResourceName()}/executeCommand`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ command: command })
        });
    }

    loadPlayers() {
        // Mock player data - in real implementation, this would come from server
        this.players = [
            { id: 1, name: 'Player1', ping: 45, steamId: 'steam:123456789' },
            { id: 2, name: 'Player2', ping: 67, steamId: 'steam:987654321' },
            { id: 3, name: 'Player3', ping: 23, steamId: 'steam:456789123' }
        ];
        
        this.renderPlayers();
    }

    renderPlayers() {
        const playerList = document.getElementById('playerList');
        playerList.innerHTML = '';

        this.players.forEach(player => {
            const playerItem = document.createElement('div');
            playerItem.className = 'player-item';
            playerItem.innerHTML = `
                <div class="player-info">
                    <div class="player-avatar">${player.name.charAt(0).toUpperCase()}</div>
                    <div class="player-details">
                        <h4>${player.name}</h4>
                        <span>ID: ${player.id} | Ping: ${player.ping}ms</span>
                    </div>
                </div>
                <div class="player-actions">
                    <button onclick="adminMenu.showPlayerContextMenu(event, ${player.id})">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                </div>
            `;
            
            playerList.appendChild(playerItem);
        });
    }

    filterPlayers(searchTerm) {
        const filteredPlayers = this.players.filter(player => 
            player.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            player.id.toString().includes(searchTerm)
        );
        
        const playerList = document.getElementById('playerList');
        playerList.innerHTML = '';

        filteredPlayers.forEach(player => {
            const playerItem = document.createElement('div');
            playerItem.className = 'player-item';
            playerItem.innerHTML = `
                <div class="player-info">
                    <div class="player-avatar">${player.name.charAt(0).toUpperCase()}</div>
                    <div class="player-details">
                        <h4>${player.name}</h4>
                        <span>ID: ${player.id} | Ping: ${player.ping}ms</span>
                    </div>
                </div>
                <div class="player-actions">
                    <button onclick="adminMenu.showPlayerContextMenu(event, ${player.id})">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                </div>
            `;
            
            playerList.appendChild(playerItem);
        });
    }

    showPlayerContextMenu(event, playerId) {
        event.stopPropagation();
        this.selectedPlayer = playerId;
        
        const contextMenu = document.getElementById('playerContextMenu');
        contextMenu.classList.remove('hidden');
        contextMenu.style.left = event.pageX + 'px';
        contextMenu.style.top = event.pageY + 'px';

        // Bind context menu actions
        document.querySelectorAll('.context-item').forEach(item => {
            item.onclick = (e) => {
                const action = e.target.closest('.context-item').dataset.action;
                this.handlePlayerAction(action, playerId);
                this.hideContextMenu();
            };
        });
    }

    hideContextMenu() {
        document.getElementById('playerContextMenu').classList.add('hidden');
    }

    handlePlayerAction(action, playerId) {
        const player = this.players.find(p => p.id === playerId);
        if (!player) return;

        switch (action) {
            case 'kick':
                this.showModal('Spieler kicken', `Grund für Kick von ${player.name}:`, (reason) => {
                    this.executeCommand(`kick ${playerId} ${reason}`);
                });
                break;
            case 'ban':
                this.showModal('Spieler bannen', `Grund für Ban von ${player.name}:`, (reason) => {
                    this.executeCommand(`ban ${playerId} 0 ${reason}`);
                });
                break;
            case 'teleport':
                this.executeCommand(`tpto ${playerId}`);
                break;
            case 'bring':
                this.executeCommand(`tphere ${playerId}`);
                break;
            case 'heal':
                this.executeCommand(`heal ${playerId}`);
                break;
            case 'spectate':
                this.executeCommand(`spectate ${playerId}`);
                break;
            case 'freeze':
                this.executeCommand(`freeze ${playerId}`);
                break;
        }
    }

    showModal(title, message, callback) {
        document.getElementById('modalTitle').textContent = title;
        document.getElementById('modalInput').placeholder = message;
        document.getElementById('modal').classList.remove('hidden');

        document.getElementById('modalConfirm').onclick = () => {
            const value = document.getElementById('modalInput').value;
            if (value.trim() && callback) {
                callback(value);
            }
            this.hideModal();
        };
    }

    hideModal() {
        document.getElementById('modal').classList.add('hidden');
        document.getElementById('modalInput').value = '';
    }

    isModalOpen() {
        return !document.getElementById('modal').classList.contains('hidden');
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;

        document.getElementById('notifications').appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 5000);
    }
}

// Initialize admin menu
const adminMenu = new AdminMenu();

// Message handler for FiveM
window.addEventListener('message', (event) => {
    const data = event.data;

    switch (data.type) {
        case 'openMenu':
            adminMenu.openMenu();
            break;
        case 'notification':
            adminMenu.showNotification(data.message, data.notificationType);
            break;
        case 'copyToClipboard':
            if (navigator.clipboard) {
                navigator.clipboard.writeText(data.text);
                adminMenu.showNotification('In Zwischenablage kopiert', 'success');
            }
            break;
    }
});

// Helper function for FiveM resource name
function GetParentResourceName() {
    return 'advanced_admin'; // Replace with your resource name
}
