-- Advanced Admin Script - NoClip System
-- Handles noclip functionality

local noclipEnabled = false
local noclipSpeed = 1.0
local noclipEntity = nil

-- NoC<PERSON> controls
local controls = {
    forward = 32,   -- W
    backward = 33,  -- S
    left = 34,      -- A
    right = 35,     -- D
    up = 44,        -- Q
    down = 46,      -- E
    speedUp = 21,   -- Left Shift
    speedDown = 19, -- Left Alt
}

-- Start noclip
function StartNoclip()
    noclipEnabled = true
    noclipEntity = PlayerPedId()
    
    -- Make entity invisible and disable collision
    SetEntityVisible(noclipEntity, false, false)
    SetEntityCollision(noclipEntity, false, false)
    FreezeEntityPosition(noclipEntity, true)
    SetEntityInvincible(noclipEntity, true)
    
    -- Start noclip thread
    Citizen.CreateThread(NoclipThread)
end

-- Stop noclip
function StopNoclip()
    noclipEnabled = false
    
    if noclipEntity then
        -- Restore entity properties
        SetEntityVisible(noclipEntity, true, false)
        SetEntityCollision(noclipEntity, true, true)
        FreezeEntityPosition(noclipEntity, false)
        SetEntityInvincible(noclipEntity, false)
        
        -- Place entity on ground
        local coords = GetEntityCoords(noclipEntity)
        local ground, groundZ = GetGroundZFor_3dCoord(coords.x, coords.y, coords.z, false)
        if ground then
            SetEntityCoords(noclipEntity, coords.x, coords.y, groundZ, false, false, false, true)
        end
        
        noclipEntity = nil
    end
end

-- NoClip movement thread
function NoclipThread()
    while noclipEnabled do
        Citizen.Wait(0)
        
        if not noclipEntity or not DoesEntityExist(noclipEntity) then
            break
        end
        
        local coords = GetEntityCoords(noclipEntity)
        local heading = GetEntityHeading(noclipEntity)
        local speed = noclipSpeed
        
        -- Speed modifiers
        if IsControlPressed(0, controls.speedUp) then
            speed = speed * 3.0
        elseif IsControlPressed(0, controls.speedDown) then
            speed = speed * 0.3
        end
        
        -- Movement calculations
        local newCoords = coords
        local moved = false
        
        -- Forward/Backward
        if IsControlPressed(0, controls.forward) then
            local forwardVector = GetEntityForwardVector(noclipEntity)
            newCoords = vector3(
                coords.x + forwardVector.x * speed,
                coords.y + forwardVector.y * speed,
                coords.z + forwardVector.z * speed
            )
            moved = true
        elseif IsControlPressed(0, controls.backward) then
            local forwardVector = GetEntityForwardVector(noclipEntity)
            newCoords = vector3(
                coords.x - forwardVector.x * speed,
                coords.y - forwardVector.y * speed,
                coords.z - forwardVector.z * speed
            )
            moved = true
        end
        
        -- Left/Right
        if IsControlPressed(0, controls.left) then
            local rightVector = GetEntityRightVector(noclipEntity)
            newCoords = vector3(
                newCoords.x - rightVector.x * speed,
                newCoords.y - rightVector.y * speed,
                newCoords.z - rightVector.z * speed
            )
            moved = true
        elseif IsControlPressed(0, controls.right) then
            local rightVector = GetEntityRightVector(noclipEntity)
            newCoords = vector3(
                newCoords.x + rightVector.x * speed,
                newCoords.y + rightVector.y * speed,
                newCoords.z + rightVector.z * speed
            )
            moved = true
        end
        
        -- Up/Down
        if IsControlPressed(0, controls.up) then
            newCoords = vector3(newCoords.x, newCoords.y, newCoords.z + speed)
            moved = true
        elseif IsControlPressed(0, controls.down) then
            newCoords = vector3(newCoords.x, newCoords.y, newCoords.z - speed)
            moved = true
        end
        
        -- Apply movement
        if moved then
            SetEntityCoords(noclipEntity, newCoords.x, newCoords.y, newCoords.z, false, false, false, true)
        end
        
        -- Disable some controls while in noclip
        DisableControlAction(0, 75, true)  -- Exit Vehicle
        DisableControlAction(0, 23, true)  -- Enter Vehicle
        DisableControlAction(0, 24, true)  -- Attack
        DisableControlAction(0, 25, true)  -- Aim
        DisableControlAction(0, 47, true)  -- Weapon Wheel
        DisableControlAction(0, 58, true)  -- Weapon Wheel
        DisableControlAction(0, 263, true) -- Melee Attack 1
        DisableControlAction(0, 264, true) -- Melee Attack 2
        DisableControlAction(0, 257, true) -- Attack 2
        DisableControlAction(0, 140, true) -- Melee Attack Light
        DisableControlAction(0, 141, true) -- Melee Attack Heavy
        DisableControlAction(0, 142, true) -- Melee Attack Alternate
        DisableControlAction(0, 143, true) -- Melee Block
    end
end

-- Helper function to get entity forward vector
function GetEntityForwardVector(entity)
    local heading = math.rad(GetEntityHeading(entity))
    return vector3(-math.sin(heading), math.cos(heading), 0.0)
end

-- Helper function to get entity right vector
function GetEntityRightVector(entity)
    local heading = math.rad(GetEntityHeading(entity))
    return vector3(math.cos(heading), math.sin(heading), 0.0)
end

-- Export functions
exports('StartNoclip', StartNoclip)
exports('StopNoclip', StopNoclip)
exports('IsNoclipEnabled', function() return noclipEnabled end)

-- Global access
_G.StartNoclip = StartNoclip
_G.StopNoclip = StopNoclip
