@echo off
:: ====================================================================
:: Winget Update Script
:: Aktualisiert alle Programme über Windows Package Manager
:: ====================================================================

echo.
echo ====================================================================
echo                        WINGET UPDATES
echo ====================================================================
echo.

:: <PERSON>rüfen ob Winget verfügbar ist
winget --version >nul 2>&1
if %errorLevel% neq 0 (
    echo [FEHLER] Winget ist nicht verfuegbar oder nicht installiert!
    echo Bitte Windows App Installer aus dem Microsoft Store installieren.
    echo.
    exit /b 1
)

echo [INFO] Winget Version:
winget --version
echo.

:: Winget Source aktualisieren
echo [INFO] Aktualisiere Winget Sources...
winget source update
echo.

:: Liste aller verfügbaren Updates anzeigen
echo [INFO] Verfuegbare Updates:
winget upgrade
echo.

:: Alle Updates installieren
echo [INFO] Installiere alle verfuegbaren Updates...
echo.

:: Wichtige Programme einzeln updaten für bessere Kontrolle
echo [INFO] Update Google Chrome...
winget upgrade --id Google.Chrome --silent --accept-package-agreements --accept-source-agreements

echo [INFO] Update Mozilla Firefox...
winget upgrade --id Mozilla.Firefox --silent --accept-package-agreements --accept-source-agreements

echo [INFO] Update Microsoft Edge...
winget upgrade --id Microsoft.Edge --silent --accept-package-agreements --accept-source-agreements

echo [INFO] Update VLC Media Player...
winget upgrade --id VideoLAN.VLC --silent --accept-package-agreements --accept-source-agreements

echo [INFO] Update 7-Zip...
winget upgrade --id 7zip.7zip --silent --accept-package-agreements --accept-source-agreements

echo [INFO] Update Notepad++...
winget upgrade --id Notepad++.Notepad++ --silent --accept-package-agreements --accept-source-agreements

echo [INFO] Update Adobe Acrobat Reader...
winget upgrade --id Adobe.Acrobat.Reader.64-bit --silent --accept-package-agreements --accept-source-agreements

echo [INFO] Update Java Runtime...
winget upgrade --id Oracle.JavaRuntimeEnvironment --silent --accept-package-agreements --accept-source-agreements

echo [INFO] Update Microsoft Visual C++ Redistributables...
winget upgrade --id Microsoft.VCRedist.2015+.x64 --silent --accept-package-agreements --accept-source-agreements
winget upgrade --id Microsoft.VCRedist.2015+.x86 --silent --accept-package-agreements --accept-source-agreements

echo.
echo [INFO] Fuehre globales Update fuer alle verbleibenden Programme aus...
winget upgrade --all --silent --accept-package-agreements --accept-source-agreements --include-unknown

echo.
echo [INFO] Winget Updates abgeschlossen!
echo.

:: Finale Überprüfung
echo [INFO] Finale Ueberpruefung der Updates:
winget upgrade
echo.

exit /b 0
