-- Advanced Admin Script - Client Commands
-- Client-side command handlers and utilities

-- Teleport to coordinates command (client-side helper)
RegisterCommand('tpcoords', function(source, args, rawCommand)
    local x, y, z = tonumber(args[1]), tonumber(args[2]), tonumber(args[3])
    if x and y and z then
        local ped = PlayerPedId()
        SetEntityCoords(ped, x, y, z, false, false, false, true)
        ShowNotification(string.format("Teleportiert zu %.2f, %.2f, %.2f", x, y, z), 'success')
    else
        ShowNotification("Usage: /tpcoords <x> <y> <z>", 'error')
    end
end, false)

-- Get current coordinates
RegisterCommand('coords', function(source, args, rawCommand)
    local ped = PlayerPedId()
    local coords = GetEntityCoords(ped)
    local heading = GetEntityHeading(ped)
    
    local coordsText = string.format("X: %.2f, Y: %.2f, Z: %.2f, H: %.2f", coords.x, coords.y, coords.z, heading)
    ShowNotification(coordsText, 'info')
    
    -- Copy to clipboard (if supported)
    SendNUIMessage({
        type = "copyToClipboard",
        text = string.format("%.2f, %.2f, %.2f, %.2f", coords.x, coords.y, coords.z, heading)
    })
    
    print("^2[Coords]^7 " .. coordsText)
end, false)

-- Teleport to waypoint
RegisterCommand('tpwp', function(source, args, rawCommand)
    local waypoint = GetFirstBlipInfoId(8) -- Waypoint blip
    if waypoint and DoesBlipExist(waypoint) then
        local coords = GetBlipCoords(waypoint)
        local ped = PlayerPedId()
        
        -- Get ground Z coordinate
        local ground, groundZ = GetGroundZFor_3dCoord(coords.x, coords.y, coords.z, false)
        if ground then
            coords = vector3(coords.x, coords.y, groundZ)
        end
        
        SetEntityCoords(ped, coords.x, coords.y, coords.z, false, false, false, true)
        ShowNotification("Zu Wegpunkt teleportiert", 'success')
    else
        ShowNotification("Kein Wegpunkt gesetzt", 'error')
    end
end, false)

-- Save current position
local savedPositions = {}

RegisterCommand('savepos', function(source, args, rawCommand)
    local name = args[1] or "default"
    local ped = PlayerPedId()
    local coords = GetEntityCoords(ped)
    local heading = GetEntityHeading(ped)
    
    savedPositions[name] = {
        x = coords.x,
        y = coords.y,
        z = coords.z,
        heading = heading
    }
    
    ShowNotification(string.format("Position '%s' gespeichert", name), 'success')
end, false)

-- Load saved position
RegisterCommand('loadpos', function(source, args, rawCommand)
    local name = args[1] or "default"
    
    if savedPositions[name] then
        local pos = savedPositions[name]
        local ped = PlayerPedId()
        
        SetEntityCoords(ped, pos.x, pos.y, pos.z, false, false, false, true)
        SetEntityHeading(ped, pos.heading)
        
        ShowNotification(string.format("Position '%s' geladen", name), 'success')
    else
        ShowNotification(string.format("Position '%s' nicht gefunden", name), 'error')
    end
end, false)

-- List saved positions
RegisterCommand('listpos', function(source, args, rawCommand)
    local count = 0
    for name, _ in pairs(savedPositions) do
        count = count + 1
    end
    
    if count == 0 then
        ShowNotification("Keine gespeicherten Positionen", 'info')
        return
    end
    
    ShowNotification(string.format("Gespeicherte Positionen (%d):", count), 'info')
    for name, pos in pairs(savedPositions) do
        ShowNotification(string.format("- %s: %.1f, %.1f, %.1f", name, pos.x, pos.y, pos.z), 'info')
    end
end, false)

-- Delete saved position
RegisterCommand('delpos', function(source, args, rawCommand)
    local name = args[1]
    if not name then
        ShowNotification("Usage: /delpos <name>", 'error')
        return
    end
    
    if savedPositions[name] then
        savedPositions[name] = nil
        ShowNotification(string.format("Position '%s' gelöscht", name), 'success')
    else
        ShowNotification(string.format("Position '%s' nicht gefunden", name), 'error')
    end
end, false)

-- Spectate player (client-side)
local spectating = false
local spectateTarget = nil

RegisterCommand('spectate', function(source, args, rawCommand)
    local targetId = tonumber(args[1])
    
    if spectating then
        StopSpectating()
        return
    end
    
    if not targetId then
        ShowNotification("Usage: /spectate <player_id>", 'error')
        return
    end
    
    local targetPlayer = GetPlayerFromServerId(targetId)
    if targetPlayer == -1 then
        ShowNotification("Spieler nicht gefunden", 'error')
        return
    end
    
    StartSpectating(targetPlayer)
end, false)

function StartSpectating(targetPlayer)
    if spectating then return end
    
    spectating = true
    spectateTarget = targetPlayer
    
    local ped = PlayerPedId()
    local targetPed = GetPlayerPed(targetPlayer)
    
    -- Hide local player
    SetEntityVisible(ped, false, false)
    SetEntityCollision(ped, false, false)
    FreezeEntityPosition(ped, true)
    
    -- Start spectating
    NetworkSetInSpectatorMode(true, targetPed)
    ShowNotification(string.format("Spectating %s", GetPlayerName(targetPlayer)), 'info')
    
    -- Spectate thread
    Citizen.CreateThread(function()
        while spectating do
            Citizen.Wait(0)
            
            -- Check if target is still valid
            if not targetPed or not DoesEntityExist(targetPed) then
                StopSpectating()
                break
            end
            
            -- Instructions
            DrawText2D(0.5, 0.05, string.format("Spectating: %s | Press [E] to stop", GetPlayerName(targetPlayer)), 0.4)
            
            -- Stop spectating
            if IsControlJustPressed(0, 38) then -- E key
                StopSpectating()
                break
            end
        end
    end)
end

function StopSpectating()
    if not spectating then return end
    
    spectating = false
    spectateTarget = nil
    
    local ped = PlayerPedId()
    
    -- Stop spectating
    NetworkSetInSpectatorMode(false, ped)
    
    -- Restore local player
    SetEntityVisible(ped, true, false)
    SetEntityCollision(ped, true, true)
    FreezeEntityPosition(ped, false)
    
    ShowNotification("Spectating beendet", 'info')
end

-- Utility function to draw 2D text
function DrawText2D(x, y, text, scale)
    SetTextFont(4)
    SetTextProportional(1)
    SetTextScale(scale or 0.4, scale or 0.4)
    SetTextColour(255, 255, 255, 255)
    SetTextDropShadow(0, 0, 0, 0, 255)
    SetTextEdge(1, 0, 0, 0, 255)
    SetTextDropShadow()
    SetTextOutline()
    SetTextEntry("STRING")
    AddTextComponentString(text)
    DrawText(x, y)
end

-- Vehicle-related commands
RegisterCommand('engine', function(source, args, rawCommand)
    local ped = PlayerPedId()
    local vehicle = GetVehiclePedIsIn(ped, false)
    
    if vehicle and vehicle ~= 0 then
        local engineOn = GetIsVehicleEngineRunning(vehicle)
        SetVehicleEngineOn(vehicle, not engineOn, false, true)
        
        local status = engineOn and "ausgeschaltet" or "eingeschaltet"
        ShowNotification(string.format("Motor %s", status), 'info')
    else
        ShowNotification("Du bist in keinem Fahrzeug", 'error')
    end
end, false)

RegisterCommand('doors', function(source, args, rawCommand)
    local ped = PlayerPedId()
    local vehicle = GetVehiclePedIsIn(ped, false)
    
    if vehicle and vehicle ~= 0 then
        local doorIndex = tonumber(args[1])
        if doorIndex and doorIndex >= 0 and doorIndex <= 5 then
            local doorAngle = GetVehicleDoorAngleRatio(vehicle, doorIndex)
            if doorAngle > 0.1 then
                SetVehicleDoorShut(vehicle, doorIndex, false)
                ShowNotification(string.format("Tür %d geschlossen", doorIndex), 'info')
            else
                SetVehicleDoorOpen(vehicle, doorIndex, false, false)
                ShowNotification(string.format("Tür %d geöffnet", doorIndex), 'info')
            end
        else
            ShowNotification("Usage: /doors <0-5>", 'error')
        end
    else
        ShowNotification("Du bist in keinem Fahrzeug", 'error')
    end
end, false)
