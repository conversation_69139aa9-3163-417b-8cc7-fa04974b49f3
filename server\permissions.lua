-- Advanced Admin Script - Permission System
-- Handles all permission-related functions

-- Add admin to config
function AddAdmin(identifier, role)
    if not Config.PermissionLevels[role] then
        return false, "Invalid role"
    end
    
    Config.Admins[identifier] = role
    SaveAdminConfig()
    return true, "<PERSON><PERSON> added successfully"
end

-- Remove admin from config
function RemoveAdmin(identifier)
    if Config.Admins[identifier] then
        Config.Admins[identifier] = nil
        SaveAdminConfig()
        return true, "Admin removed successfully"
    end
    return false, "Admin not found"
end

-- Get all admins
function GetAllAdmins()
    local admins = {}
    for identifier, role in pairs(Config.Admins) do
        table.insert(admins, {
            identifier = identifier,
            role = role,
            level = Config.PermissionLevels[role] or 0
        })
    end
    return admins
end

-- Get online admins
function GetOnlineAdmins()
    local onlineAdmins = {}
    
    for _, playerId in pairs(GetPlayers()) do
        local playerLevel = GetPlayerPermissionLevel(playerId)
        if playerLevel > 0 then
            local identifiers = GetPlayerIdentifiers(playerId)
            local role = "user"
            
            for _, identifier in pairs(identifiers) do
                if Config.Admins[identifier] then
                    role = Config.Admins[identifier]
                    break
                end
            end
            
            table.insert(onlineAdmins, {
                id = playerId,
                name = GetPlayerName(playerId),
                role = role,
                level = playerLevel
            })
        end
    end
    
    return onlineAdmins
end

-- Save admin configuration (this would typically save to a database or file)
function SaveAdminConfig()
    -- In a real implementation, you would save the Config.Admins table
    -- to a persistent storage like a database or JSON file
    print("^3[Advanced Admin]^7 Admin configuration saved")
end

-- Check if player can target another player (higher level can target lower level)
function CanTargetPlayer(adminId, targetId)
    local adminLevel = GetPlayerPermissionLevel(adminId)
    local targetLevel = GetPlayerPermissionLevel(targetId)
    
    return adminLevel > targetLevel
end

-- Get player role name
function GetPlayerRole(src)
    local identifiers = GetPlayerIdentifiers(src)
    
    for _, identifier in pairs(identifiers) do
        if Config.Admins[identifier] then
            return Config.Admins[identifier]
        end
    end
    
    return "user"
end

-- Commands for permission management
RegisterCommand('addadmin', function(source, args, rawCommand)
    if not HasPermission(source, 'superadmin') then
        SendNotification(source, GetMessage('no_permission'), 'error')
        return
    end
    
    local targetId = tonumber(args[1])
    local role = args[2]
    
    if not targetId or not GetPlayerName(targetId) then
        SendNotification(source, GetMessage('player_not_found'), 'error')
        return
    end
    
    if not role or not Config.PermissionLevels[role] then
        SendNotification(source, "Usage: /addadmin <id> <role>", 'error')
        return
    end
    
    local identifiers = GetPlayerIdentifiers(targetId)
    local primaryIdentifier = identifiers[1] -- Use first identifier as primary
    
    local success, message = AddAdmin(primaryIdentifier, role)
    if success then
        SendNotification(source, string.format("%s wurde als %s hinzugefügt", GetPlayerName(targetId), role), 'success')
        SendNotification(targetId, string.format("Du wurdest als %s hinzugefügt", role), 'info')
        LogAction(source, string.format("added %s (%s) as %s", GetPlayerName(targetId), targetId, role))
    else
        SendNotification(source, message, 'error')
    end
end, false)

RegisterCommand('removeadmin', function(source, args, rawCommand)
    if not HasPermission(source, 'superadmin') then
        SendNotification(source, GetMessage('no_permission'), 'error')
        return
    end
    
    local targetId = tonumber(args[1])
    
    if not targetId or not GetPlayerName(targetId) then
        SendNotification(source, GetMessage('player_not_found'), 'error')
        return
    end
    
    local identifiers = GetPlayerIdentifiers(targetId)
    local removed = false
    
    for _, identifier in pairs(identifiers) do
        if Config.Admins[identifier] then
            local success, message = RemoveAdmin(identifier)
            if success then
                removed = true
                break
            end
        end
    end
    
    if removed then
        SendNotification(source, string.format("%s wurde als Admin entfernt", GetPlayerName(targetId)), 'success')
        SendNotification(targetId, "Du wurdest als Admin entfernt", 'info')
        LogAction(source, string.format("removed %s (%s) from admin", GetPlayerName(targetId), targetId))
    else
        SendNotification(source, "Spieler ist kein Admin", 'error')
    end
end, false)

RegisterCommand('admins', function(source, args, rawCommand)
    if not HasPermission(source, 'helper') then
        SendNotification(source, GetMessage('no_permission'), 'error')
        return
    end
    
    local onlineAdmins = GetOnlineAdmins()
    
    if #onlineAdmins == 0 then
        SendNotification(source, "Keine Admins online", 'info')
        return
    end
    
    SendNotification(source, "^2Online Admins:", 'info')
    for _, admin in pairs(onlineAdmins) do
        SendNotification(source, string.format("^7%s (ID: %s) - %s", admin.name, admin.id, admin.role), 'info')
    end
end, false)

-- Export functions
exports('AddAdmin', AddAdmin)
exports('RemoveAdmin', RemoveAdmin)
exports('GetAllAdmins', GetAllAdmins)
exports('GetOnlineAdmins', GetOnlineAdmins)
exports('CanTargetPlayer', CanTargetPlayer)
exports('GetPlayerRole', GetPlayerRole)
