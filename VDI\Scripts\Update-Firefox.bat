@echo off
:: ====================================================================
:: Firefox Update Script
:: Aktualisiert Mozilla Firefox auf die neueste Version
:: ====================================================================

echo.
echo ====================================================================
echo                        FIREFOX UPDATE
echo ====================================================================
echo.

:: Firefox Installation prüfen
set "FIREFOX_PATH="
if exist "%ProgramFiles%\Mozilla Firefox\firefox.exe" (
    set "FIREFOX_PATH=%ProgramFiles%\Mozilla Firefox\firefox.exe"
) else if exist "%ProgramFiles(x86)%\Mozilla Firefox\firefox.exe" (
    set "FIREFOX_PATH=%ProgramFiles(x86)%\Mozilla Firefox\firefox.exe"
)

if "%FIREFOX_PATH%"=="" (
    echo [INFO] Firefox ist nicht installiert. Installiere Firefox...
    goto INSTALL_FIREFOX
) else (
    echo [INFO] Firefox gefunden: %FIREFOX_PATH%
)

:: Aktuelle Firefox Version anzeigen
echo [INFO] Aktuelle Firefox Version:
"%FIREFOX_PATH%" --version 2>nul
echo.

:: Firefox über Winget updaten (bevorzugte Methode)
echo [INFO] Versuche Firefox Update ueber Winget...
winget upgrade --id Mozilla.Firefox --silent --accept-package-agreements --accept-source-agreements
if %errorLevel% equ 0 (
    echo [SUCCESS] Firefox erfolgreich ueber Winget aktualisiert!
    goto END
)

:: Alternative: Direkter Download und Installation
:INSTALL_FIREFOX
echo [INFO] Lade neueste Firefox Version herunter...

:: Temporäres Verzeichnis erstellen
set "TEMP_DIR=%TEMP%\FirefoxUpdate"
if not exist "%TEMP_DIR%" mkdir "%TEMP_DIR%"

:: Firefox Installer herunterladen
echo [INFO] Lade Firefox Installer herunter...
powershell -Command "& {Invoke-WebRequest -Uri 'https://download.mozilla.org/?product=firefox-latest&os=win64&lang=de' -OutFile '%TEMP_DIR%\Firefox-Setup.exe'}"

if not exist "%TEMP_DIR%\Firefox-Setup.exe" (
    echo [FEHLER] Download fehlgeschlagen!
    goto CLEANUP
)

echo [INFO] Installiere Firefox...
"%TEMP_DIR%\Firefox-Setup.exe" /S

if %errorLevel% equ 0 (
    echo [SUCCESS] Firefox erfolgreich installiert/aktualisiert!
) else (
    echo [FEHLER] Firefox Installation fehlgeschlagen!
)

:CLEANUP
echo [INFO] Bereinige temporaere Dateien...
if exist "%TEMP_DIR%" rmdir /s /q "%TEMP_DIR%"

:END
echo.
echo [INFO] Firefox Update abgeschlossen!
echo.

:: Firefox Profil-Updates und Bereinigung
echo [INFO] Bereinige Firefox Cache und temporaere Dateien...

:: Firefox beenden falls läuft
taskkill /f /im firefox.exe >nul 2>&1

:: Cache-Verzeichnisse bereinigen
if exist "%APPDATA%\Mozilla\Firefox\Profiles" (
    for /d %%i in ("%APPDATA%\Mozilla\Firefox\Profiles\*") do (
        if exist "%%i\cache2" rmdir /s /q "%%i\cache2" >nul 2>&1
        if exist "%%i\startupCache" rmdir /s /q "%%i\startupCache" >nul 2>&1
        if exist "%%i\thumbnails" rmdir /s /q "%%i\thumbnails" >nul 2>&1
    )
    echo [INFO] Firefox Cache bereinigt.
)

echo.
echo [INFO] Firefox Update und Bereinigung abgeschlossen!
echo.

exit /b 0
