@echo off
:: ====================================================================
:: Additional Programs Update Script
:: Aktualisiert weitere wichtige Programme
:: ====================================================================

echo.
echo ====================================================================
echo                    ZUSAETZLICHE PROGRAMME
echo ====================================================================
echo.

:: Java Runtime Environment
echo [INFO] Aktualisiere Java Runtime Environment...
winget upgrade --id Oracle.JavaRuntimeEnvironment --silent --accept-package-agreements --accept-source-agreements 2>nul
winget upgrade --id Eclipse.Adoptium.JRE.8 --silent --accept-package-agreements --accept-source-agreements 2>nul
winget upgrade --id Eclipse.Adoptium.JRE.11 --silent --accept-package-agreements --accept-source-agreements 2>nul
winget upgrade --id Eclipse.Adoptium.JRE.17 --silent --accept-package-agreements --accept-source-agreements 2>nul

:: Google Chrome
echo [INFO] Aktualisiere Google Chrome...
winget upgrade --id Google.Chrome --silent --accept-package-agreements --accept-source-agreements 2>nul

:: Microsoft Edge
echo [INFO] Aktualisiere Microsoft Edge...
winget upgrade --id Microsoft.Edge --silent --accept-package-agreements --accept-source-agreements 2>nul

:: VLC Media Player
echo [INFO] Aktualisiere VLC Media Player...
winget upgrade --id VideoLAN.VLC --silent --accept-package-agreements --accept-source-agreements 2>nul

:: 7-Zip
echo [INFO] Aktualisiere 7-Zip...
winget upgrade --id 7zip.7zip --silent --accept-package-agreements --accept-source-agreements 2>nul

:: WinRAR
echo [INFO] Aktualisiere WinRAR...
winget upgrade --id RARLab.WinRAR --silent --accept-package-agreements --accept-source-agreements 2>nul

:: Notepad++
echo [INFO] Aktualisiere Notepad++...
winget upgrade --id Notepad++.Notepad++ --silent --accept-package-agreements --accept-source-agreements 2>nul

:: Visual Studio Code
echo [INFO] Aktualisiere Visual Studio Code...
winget upgrade --id Microsoft.VisualStudioCode --silent --accept-package-agreements --accept-source-agreements 2>nul

:: Git
echo [INFO] Aktualisiere Git...
winget upgrade --id Git.Git --silent --accept-package-agreements --accept-source-agreements 2>nul

:: Node.js
echo [INFO] Aktualisiere Node.js...
winget upgrade --id OpenJS.NodeJS --silent --accept-package-agreements --accept-source-agreements 2>nul

:: Python
echo [INFO] Aktualisiere Python...
winget upgrade --id Python.Python.3.11 --silent --accept-package-agreements --accept-source-agreements 2>nul
winget upgrade --id Python.Python.3.12 --silent --accept-package-agreements --accept-source-agreements 2>nul

:: TeamViewer
echo [INFO] Aktualisiere TeamViewer...
winget upgrade --id TeamViewer.TeamViewer --silent --accept-package-agreements --accept-source-agreements 2>nul

:: Zoom
echo [INFO] Aktualisiere Zoom...
winget upgrade --id Zoom.Zoom --silent --accept-package-agreements --accept-source-agreements 2>nul

:: Skype
echo [INFO] Aktualisiere Skype...
winget upgrade --id Microsoft.Skype --silent --accept-package-agreements --accept-source-agreements 2>nul

:: Discord
echo [INFO] Aktualisiere Discord...
winget upgrade --id Discord.Discord --silent --accept-package-agreements --accept-source-agreements 2>nul

:: Spotify
echo [INFO] Aktualisiere Spotify...
winget upgrade --id Spotify.Spotify --silent --accept-package-agreements --accept-source-agreements 2>nul

:: Steam
echo [INFO] Aktualisiere Steam...
winget upgrade --id Valve.Steam --silent --accept-package-agreements --accept-source-agreements 2>nul

:: OBS Studio
echo [INFO] Aktualisiere OBS Studio...
winget upgrade --id OBSProject.OBSStudio --silent --accept-package-agreements --accept-source-agreements 2>nul

:: Audacity
echo [INFO] Aktualisiere Audacity...
winget upgrade --id Audacity.Audacity --silent --accept-package-agreements --accept-source-agreements 2>nul

:: GIMP
echo [INFO] Aktualisiere GIMP...
winget upgrade --id GIMP.GIMP --silent --accept-package-agreements --accept-source-agreements 2>nul

:: LibreOffice
echo [INFO] Aktualisiere LibreOffice...
winget upgrade --id TheDocumentFoundation.LibreOffice --silent --accept-package-agreements --accept-source-agreements 2>nul

:: Thunderbird
echo [INFO] Aktualisiere Thunderbird...
winget upgrade --id Mozilla.Thunderbird --silent --accept-package-agreements --accept-source-agreements 2>nul

:: PowerShell
echo [INFO] Aktualisiere PowerShell...
winget upgrade --id Microsoft.PowerShell --silent --accept-package-agreements --accept-source-agreements 2>nul

:: Windows Terminal
echo [INFO] Aktualisiere Windows Terminal...
winget upgrade --id Microsoft.WindowsTerminal --silent --accept-package-agreements --accept-source-agreements 2>nul

:: Drivers über Windows Update
echo.
echo [INFO] Suche nach Treiber-Updates...
powershell -Command "& {Get-WindowsUpdate -MicrosoftUpdate | Where-Object {$_.Categories -like '*Driver*'} | Install-WindowsUpdate -AcceptAll -AutoReboot:$false}" 2>nul

:: Intel Driver & Support Assistant (falls installiert)
if exist "%ProgramFiles%\Intel\Driver and Support Assistant\DSATray.exe" (
    echo [INFO] Starte Intel Driver & Support Assistant...
    start "" "%ProgramFiles%\Intel\Driver and Support Assistant\DSATray.exe" /scan
)

:: NVIDIA GeForce Experience (falls installiert)
if exist "%ProgramFiles%\NVIDIA Corporation\NVIDIA GeForce Experience\NVIDIA GeForce Experience.exe" (
    echo [INFO] Pruefe NVIDIA Treiber Updates...
    winget upgrade --id Nvidia.GeForceExperience --silent --accept-package-agreements --accept-source-agreements 2>nul
)

:: AMD Software (falls installiert)
if exist "%ProgramFiles%\AMD\CNext\CNext\RadeonSoftware.exe" (
    echo [INFO] Pruefe AMD Treiber Updates...
    winget upgrade --id AMD.AMDSoftware --silent --accept-package-agreements --accept-source-agreements 2>nul
)

echo.
echo [INFO] Zusaetzliche Programme Updates abgeschlossen!
echo.

exit /b 0
