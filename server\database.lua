-- Advanced Admin Script - Database Functions
-- Optional database integration for persistent storage

-- Database table creation (MySQL)
local createTables = [[
CREATE TABLE IF NOT EXISTS `admin_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `player_id` int(11) NOT NULL,
  `player_name` varchar(255) NOT NULL,
  `steam_id` varchar(255) DEFAULT NULL,
  `license` varchar(255) DEFAULT NULL,
  `action` text NOT NULL,
  `timestamp` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `player_id` (`player_id`),
  KEY `timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `admin_bans` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ban_id` varchar(255) NOT NULL UNIQUE,
  `player_name` varchar(255) NOT NULL,
  `steam_id` varchar(255) DEFAULT NULL,
  `license` varchar(255) DEFAULT NULL,
  `reason` text NOT NULL,
  `admin_name` varchar(255) NOT NULL,
  `admin_id` varchar(255) NOT NULL,
  `ban_date` datetime NOT NULL,
  `expire_date` datetime DEFAULT NULL,
  `active` tinyint(1) DEFAULT 1,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ban_id` (`ban_id`),
  KEY `steam_id` (`steam_id`),
  KEY `license` (`license`),
  KEY `active` (`active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `admin_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `identifier` varchar(255) NOT NULL,
  `role` varchar(50) NOT NULL,
  `granted_by` varchar(255) NOT NULL,
  `granted_date` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `identifier` (`identifier`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
]]

-- Initialize database
Citizen.CreateThread(function()
    if Config.LogToDatabase and MySQL then
        print("^2[Advanced Admin]^7 Initializing database...")
        
        MySQL.Async.execute(createTables, {}, function(affectedRows)
            print("^2[Advanced Admin]^7 Database tables created/verified")
            LoadAdminsFromDatabase()
            LoadBansFromDatabase()
        end)
    end
end)

-- Load admins from database
function LoadAdminsFromDatabase()
    if not MySQL then return end
    
    MySQL.Async.fetchAll('SELECT * FROM admin_permissions', {}, function(results)
        if results then
            for _, row in pairs(results) do
                Config.Admins[row.identifier] = row.role
            end
            print(string.format("^2[Advanced Admin]^7 Loaded %d admins from database", #results))
        end
    end)
end

-- Save admin to database
function SaveAdminToDatabase(identifier, role, grantedBy)
    if not MySQL then return end
    
    MySQL.Async.execute('INSERT INTO admin_permissions (identifier, role, granted_by, granted_date) VALUES (@identifier, @role, @granted_by, @granted_date) ON DUPLICATE KEY UPDATE role = @role, granted_by = @granted_by, granted_date = @granted_date', {
        ['@identifier'] = identifier,
        ['@role'] = role,
        ['@granted_by'] = grantedBy,
        ['@granted_date'] = os.date("%Y-%m-%d %H:%M:%S")
    })
end

-- Remove admin from database
function RemoveAdminFromDatabase(identifier)
    if not MySQL then return end
    
    MySQL.Async.execute('DELETE FROM admin_permissions WHERE identifier = @identifier', {
        ['@identifier'] = identifier
    })
end

-- Load bans from database
function LoadBansFromDatabase()
    if not MySQL then return end
    
    MySQL.Async.fetchAll('SELECT * FROM admin_bans WHERE active = 1 AND (expire_date IS NULL OR expire_date > NOW())', {}, function(results)
        if results then
            AdminScript.Bans = {}
            for _, row in pairs(results) do
                if row.steam_id then
                    AdminScript.Bans[row.steam_id] = {
                        id = row.ban_id,
                        reason = row.reason,
                        admin = row.admin_name,
                        date = row.ban_date,
                        expires = row.expire_date and os.time{year=string.sub(row.expire_date,1,4), month=string.sub(row.expire_date,6,7), day=string.sub(row.expire_date,9,10), hour=string.sub(row.expire_date,12,13), min=string.sub(row.expire_date,15,16), sec=string.sub(row.expire_date,18,19)} or 0
                    }
                end
                if row.license then
                    AdminScript.Bans[row.license] = {
                        id = row.ban_id,
                        reason = row.reason,
                        admin = row.admin_name,
                        date = row.ban_date,
                        expires = row.expire_date and os.time{year=string.sub(row.expire_date,1,4), month=string.sub(row.expire_date,6,7), day=string.sub(row.expire_date,9,10), hour=string.sub(row.expire_date,12,13), min=string.sub(row.expire_date,15,16), sec=string.sub(row.expire_date,18,19)} or 0
                    }
                end
            end
            print(string.format("^2[Advanced Admin]^7 Loaded %d active bans from database", #results))
        end
    end)
end

-- Save ban to database
function SaveBanToDatabase(banId, playerName, identifiers, reason, adminName, adminId, duration)
    if not MySQL then return end
    
    local steamId = ""
    local license = ""
    
    for _, identifier in pairs(identifiers) do
        if string.find(identifier, "steam:") then
            steamId = identifier
        elseif string.find(identifier, "license:") then
            license = identifier
        end
    end
    
    local expireDate = duration > 0 and os.date("%Y-%m-%d %H:%M:%S", os.time() + (duration * 24 * 60 * 60)) or nil
    
    MySQL.Async.execute('INSERT INTO admin_bans (ban_id, player_name, steam_id, license, reason, admin_name, admin_id, ban_date, expire_date, active) VALUES (@ban_id, @player_name, @steam_id, @license, @reason, @admin_name, @admin_id, @ban_date, @expire_date, 1)', {
        ['@ban_id'] = banId,
        ['@player_name'] = playerName,
        ['@steam_id'] = steamId,
        ['@license'] = license,
        ['@reason'] = reason,
        ['@admin_name'] = adminName,
        ['@admin_id'] = adminId,
        ['@ban_date'] = os.date("%Y-%m-%d %H:%M:%S"),
        ['@expire_date'] = expireDate
    })
end

-- Remove ban from database
function RemoveBanFromDatabase(identifier)
    if not MySQL then return end
    
    MySQL.Async.execute('UPDATE admin_bans SET active = 0 WHERE (steam_id = @identifier OR license = @identifier) AND active = 1', {
        ['@identifier'] = identifier
    })
end

-- Get ban history for player
function GetPlayerBanHistory(identifier, callback)
    if not MySQL then 
        callback({})
        return 
    end
    
    MySQL.Async.fetchAll('SELECT * FROM admin_bans WHERE steam_id = @identifier OR license = @identifier ORDER BY ban_date DESC', {
        ['@identifier'] = identifier
    }, function(results)
        callback(results or {})
    end)
end

-- Get admin action history
function GetAdminActionHistory(adminId, limit, callback)
    if not MySQL then 
        callback({})
        return 
    end
    
    limit = limit or 50
    
    MySQL.Async.fetchAll('SELECT * FROM admin_logs WHERE player_id = @admin_id ORDER BY timestamp DESC LIMIT @limit', {
        ['@admin_id'] = adminId,
        ['@limit'] = limit
    }, function(results)
        callback(results or {})
    end)
end

-- Commands for database management
RegisterCommand('dbstats', function(source, args, rawCommand)
    if not HasPermission(source, 'superadmin') then
        SendNotification(source, GetMessage('no_permission'), 'error')
        return
    end
    
    if not MySQL then
        SendNotification(source, "Database not available", 'error')
        return
    end
    
    MySQL.Async.fetchScalar('SELECT COUNT(*) FROM admin_logs', {}, function(logCount)
        MySQL.Async.fetchScalar('SELECT COUNT(*) FROM admin_bans WHERE active = 1', {}, function(banCount)
            MySQL.Async.fetchScalar('SELECT COUNT(*) FROM admin_permissions', {}, function(adminCount)
                SendNotification(source, "^2Database Statistics:", 'info')
                SendNotification(source, string.format("^7Admin Logs: %s", logCount or 0), 'info')
                SendNotification(source, string.format("^7Active Bans: %s", banCount or 0), 'info')
                SendNotification(source, string.format("^7Admin Permissions: %s", adminCount or 0), 'info')
            end)
        end)
    end)
end, false)

-- Export functions
exports('SaveAdminToDatabase', SaveAdminToDatabase)
exports('RemoveAdminFromDatabase', RemoveAdminFromDatabase)
exports('SaveBanToDatabase', SaveBanToDatabase)
exports('RemoveBanFromDatabase', RemoveBanFromDatabase)
exports('GetPlayerBanHistory', GetPlayerBanHistory)
exports('GetAdminActionHistory', GetAdminActionHistory)
