Config = {}

-- General Settings
Config.Locale = 'de' -- Language (de, en)
Config.CommandPrefix = '/' -- Command prefix
Config.EnableLogging = true -- Enable action logging
Config.LogToFile = true -- Log to file
Config.LogToDatabase = false -- Log to database (requires mysql-async)

-- Permission Levels
Config.PermissionLevels = {
    ['superadmin'] = 100,
    ['admin'] = 80,
    ['moderator'] = 60,
    ['helper'] = 40,
    ['vip'] = 20,
    ['user'] = 0
}

-- Admin Groups (Steam IDs or License IDs)
Config.Admins = {
    -- Example: ['steam:110000100000000'] = 'superadmin',
    -- Example: ['license:abcdef1234567890'] = 'admin',
}

-- Commands Configuration
Config.Commands = {
    -- Basic Commands
    kick = { permission = 'moderator', enabled = true },
    ban = { permission = 'admin', enabled = true },
    unban = { permission = 'admin', enabled = true },
    
    -- Teleport Commands
    tp = { permission = 'moderator', enabled = true },
    tpto = { permission = 'moderator', enabled = true },
    tphere = { permission = 'moderator', enabled = true },
    goto = { permission = 'moderator', enabled = true },
    
    -- Player Commands
    heal = { permission = 'moderator', enabled = true },
    armor = { permission = 'moderator', enabled = true },
    revive = { permission = 'moderator', enabled = true },
    freeze = { permission = 'moderator', enabled = true },
    unfreeze = { permission = 'moderator', enabled = true },
    
    -- Vehicle Commands
    car = { permission = 'moderator', enabled = true },
    dv = { permission = 'moderator', enabled = true },
    fix = { permission = 'moderator', enabled = true },
    
    -- Special Commands
    noclip = { permission = 'admin', enabled = true },
    god = { permission = 'admin', enabled = true },
    invisible = { permission = 'admin', enabled = true },
    
    -- Server Commands
    weather = { permission = 'admin', enabled = true },
    time = { permission = 'admin', enabled = true },
    announce = { permission = 'moderator', enabled = true },
    
    -- Admin Menu
    admin = { permission = 'helper', enabled = true },
}

-- UI Settings
Config.UI = {
    position = 'top-right', -- top-left, top-right, bottom-left, bottom-right
    theme = 'dark', -- dark, light
    showNotifications = true,
    notificationDuration = 5000 -- milliseconds
}

-- Logging Settings
Config.Logging = {
    logFile = 'logs/admin_actions.log',
    logFormat = '[%s] %s: %s', -- [timestamp] player: action
    maxLogSize = 10485760, -- 10MB in bytes
}

-- Ban Settings
Config.Bans = {
    defaultReason = 'Verstoß gegen die Serverregeln',
    maxBanTime = 365, -- days (0 = permanent)
    banFile = 'bans.json'
}

-- Teleport Locations
Config.TeleportLocations = {
    ['spawn'] = { x = -1037.0, y = -2737.0, z = 20.0, heading = 0.0 },
    ['airport'] = { x = -1336.0, y = -3044.0, z = 13.9, heading = 0.0 },
    ['hospital'] = { x = 1839.0, y = 3672.0, z = 34.0, heading = 0.0 },
    ['police'] = { x = 425.0, y = -979.0, z = 30.0, heading = 0.0 },
}

-- Weather Types
Config.WeatherTypes = {
    'CLEAR', 'EXTRASUNNY', 'CLOUDS', 'OVERCAST', 'RAIN', 'CLEARING',
    'THUNDER', 'SMOG', 'FOGGY', 'XMAS', 'SNOWLIGHT', 'BLIZZARD'
}

-- Messages
Config.Messages = {
    ['de'] = {
        no_permission = 'Du hast keine Berechtigung für diesen Befehl.',
        player_not_found = 'Spieler nicht gefunden.',
        invalid_id = 'Ungültige Spieler ID.',
        kicked = 'Du wurdest vom Server gekickt. Grund: %s',
        banned = 'Du wurdest vom Server gebannt. Grund: %s',
        teleported = 'Du wurdest teleportiert.',
        healed = 'Du wurdest geheilt.',
        god_enabled = 'God Mode aktiviert.',
        god_disabled = 'God Mode deaktiviert.',
        noclip_enabled = 'NoClip aktiviert.',
        noclip_disabled = 'NoClip deaktiviert.',
        vehicle_spawned = 'Fahrzeug gespawnt.',
        weather_changed = 'Wetter geändert zu: %s',
        time_changed = 'Zeit geändert zu: %02d:%02d',
    },
    ['en'] = {
        no_permission = 'You do not have permission to use this command.',
        player_not_found = 'Player not found.',
        invalid_id = 'Invalid player ID.',
        kicked = 'You have been kicked from the server. Reason: %s',
        banned = 'You have been banned from the server. Reason: %s',
        teleported = 'You have been teleported.',
        healed = 'You have been healed.',
        god_enabled = 'God mode enabled.',
        god_disabled = 'God mode disabled.',
        noclip_enabled = 'NoClip enabled.',
        noclip_disabled = 'NoClip disabled.',
        vehicle_spawned = 'Vehicle spawned.',
        weather_changed = 'Weather changed to: %s',
        time_changed = 'Time changed to: %02d:%02d',
    }
}
