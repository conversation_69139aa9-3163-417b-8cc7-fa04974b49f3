@echo off
:: ====================================================================
:: Quick Update Script
:: Schnelle Updates für die wichtigsten Programme
:: ====================================================================

title Quick Update - Schnelle Updates
color 0B

echo.
echo ====================================================================
echo                        QUICK UPDATE
echo ====================================================================
echo.
echo Schnelle Updates fuer die wichtigsten Programme:
echo - Winget Programme
echo - Firefox
echo - Windows Defender
echo - Browser Cache-Bereinigung
echo.
echo ====================================================================
echo.

:: Admin-Rechte prüfen
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo [FEHLER] Administrator-Rechte erforderlich!
    pause
    exit /b 1
)

echo [INFO] Starte Quick Updates...
echo.

:: Winget Updates (nur wichtige Programme)
echo [1/5] Winget Updates...
winget upgrade --id Google.Chrome --silent --accept-package-agreements --accept-source-agreements >nul 2>&1
winget upgrade --id Mozilla.Firefox --silent --accept-package-agreements --accept-source-agreements >nul 2>&1
winget upgrade --id Microsoft.Edge --silent --accept-package-agreements --accept-source-agreements >nul 2>&1
winget upgrade --id Adobe.Acrobat.Reader.64-bit --silent --accept-package-agreements --accept-source-agreements >nul 2>&1

:: Windows Defender
echo [2/5] Windows Defender Update...
powershell -Command "Update-MpSignature" >nul 2>&1

:: Browser Cache bereinigen
echo [3/5] Browser Cache bereinigen...
taskkill /f /im chrome.exe >nul 2>&1
taskkill /f /im firefox.exe >nul 2>&1
taskkill /f /im msedge.exe >nul 2>&1

if exist "%LOCALAPPDATA%\Google\Chrome\User Data\Default\Cache" (
    rmdir /s /q "%LOCALAPPDATA%\Google\Chrome\User Data\Default\Cache" >nul 2>&1
)

:: Temp-Dateien bereinigen
echo [4/5] Temp-Dateien bereinigen...
del /f /s /q "%TEMP%\*.*" >nul 2>&1
del /f /s /q "%SystemRoot%\Temp\*.*" >nul 2>&1

:: DNS Cache leeren
echo [5/5] DNS Cache leeren...
ipconfig /flushdns >nul 2>&1

echo.
echo [SUCCESS] Quick Update abgeschlossen!
echo.
pause
