# VDI Auto-Update Scripts

Umfassende Batch-Scripts für automatische Updates aller wichtigen Programme und Windows-Komponenten.

## 📁 Übersicht

### Haupt-<PERSON><PERSON><PERSON>
- **`AutoUpdate-Master.bat`** - Vollständiges Update aller Programme
- **`Quick-Update.bat`** - Schnelle Updates der wichtigsten Programme

### Modul-Scripts (Scripts/)
- **`Update-Winget.bat`** - Winget Package Manager Updates
- **`Update-Firefox.bat`** - Mozilla Firefox Updates
- **`Update-Office.bat`** - Microsoft Office Updates
- **`Update-Adobe.bat`** - Adobe Reader Updates
- **`Update-Windows.bat`** - Windows Updates & Defender
- **`Update-Additional.bat`** - Zusätzliche Programme
- **`Cleanup-System.bat`** - System-Bereinigung

## 🚀 Verwendung

### Vollständiges Update
```batch
# Als Administrator ausführen:
AutoUpdate-Master.bat
```

### Schnelles Update
```batch
# Als Administrator ausführen:
Quick-Update.bat
```

## 📋 Was wird aktualisiert?

### Windows & System
- ✅ Windows Updates (alle verfügbaren)
- ✅ Windows Defender Signaturen
- ✅ Microsoft Store Apps
- ✅ .NET Framework Updates
- ✅ Visual C++ Redistributables
- ✅ Treiber-Updates

### Browser
- ✅ Mozilla Firefox
- ✅ Google Chrome
- ✅ Microsoft Edge

### Office & Produktivität
- ✅ Microsoft Office (365/2019/2021/2016)
- ✅ Adobe Acrobat Reader
- ✅ LibreOffice
- ✅ Notepad++

### Entwicklung
- ✅ Visual Studio Code
- ✅ Git
- ✅ Node.js
- ✅ Python
- ✅ Java Runtime Environment

### Multimedia
- ✅ VLC Media Player
- ✅ OBS Studio
- ✅ Audacity
- ✅ GIMP

### Kommunikation
- ✅ Zoom
- ✅ Skype
- ✅ Discord
- ✅ Thunderbird

### Utilities
- ✅ 7-Zip
- ✅ WinRAR
- ✅ TeamViewer
- ✅ PowerShell
- ✅ Windows Terminal

### Gaming
- ✅ Steam
- ✅ NVIDIA GeForce Experience
- ✅ AMD Software

## 🛠️ Funktionen

### Automatische Erkennung
- Erkennt installierte Programme automatisch
- Überspringt nicht installierte Software
- Verwendet optimale Update-Methoden

### Mehrere Update-Quellen
1. **Winget** (bevorzugt)
2. **Direkter Download**
3. **Windows Update**
4. **Herstellerspezifische Updater**

### Logging
- Detaillierte Logs in `Logs/` Ordner
- Zeitstempel für alle Aktionen
- Fehlerprotokollierung

### System-Bereinigung
- Temporäre Dateien
- Browser-Cache
- Windows Update Cache
- Registry-Bereinigung
- Papierkorb leeren

## ⚙️ Konfiguration

### Voraussetzungen
- **Windows 10/11**
- **Administrator-Rechte**
- **Winget** (Windows App Installer)
- **PowerShell 5.1+**

### Automatische Installation fehlender Komponenten
Die Scripts installieren automatisch:
- PSWindowsUpdate PowerShell-Modul
- Fehlende Programme über Winget

## 📊 Beispiel-Ausgabe

```
====================================================================
                    AUTO UPDATE MASTER SCRIPT
====================================================================

[1/7] Starte Winget Updates...
[INFO] Winget Version: v1.6.2771
[INFO] Update Google Chrome... ✓
[INFO] Update Mozilla Firefox... ✓
[INFO] Update Adobe Reader... ✓

[2/7] Starte Firefox Update...
[INFO] Firefox gefunden: C:\Program Files\Mozilla Firefox\firefox.exe
[SUCCESS] Firefox erfolgreich aktualisiert!

[3/7] Starte Office Update...
[INFO] Office 365 gefunden
[SUCCESS] Office Click-to-Run Update gestartet!

[4/7] Starte Adobe Reader Update...
[SUCCESS] Adobe Reader erfolgreich aktualisiert!

[5/7] Starte Windows Updates...
[INFO] 5 Updates verfügbar
[SUCCESS] Windows Updates installiert!

[6/7] Starte zusätzliche Programme...
[INFO] 12 Programme aktualisiert

[7/7] Bereinige temporäre Dateien...
[INFO] 2.3 GB temporäre Dateien bereinigt

====================================================================
                        UPDATE ABGESCHLOSSEN
====================================================================
```

## 🔧 Anpassung

### Eigene Programme hinzufügen
Bearbeiten Sie `Update-Additional.bat`:
```batch
echo [INFO] Aktualisiere MeinProgramm...
winget upgrade --id Hersteller.MeinProgramm --silent --accept-package-agreements --accept-source-agreements
```

### Programme ausschließen
Kommentieren Sie Zeilen aus:
```batch
REM winget upgrade --id Programm.Name --silent
```

### Zeitgesteuerte Ausführung
Erstellen Sie eine geplante Aufgabe:
```batch
schtasks /create /tn "Auto Update" /tr "C:\Temp\scripting\VDI\AutoUpdate-Master.bat" /sc weekly /d SUN /st 02:00 /ru SYSTEM
```

## 🚨 Sicherheitshinweise

### Administrator-Rechte
- Alle Scripts benötigen Administrator-Rechte
- Rechtsklick → "Als Administrator ausführen"

### Antivirus-Software
- Manche Antivirus-Programme blockieren Batch-Scripts
- Fügen Sie den VDI-Ordner zu Ausnahmen hinzu

### Neustart erforderlich
- Einige Updates erfordern einen Neustart
- Script fragt automatisch nach Neustart

## 📝 Logs

### Log-Dateien
```
Logs/
├── AutoUpdate_2024-01-15_14-30-25.log
├── AutoUpdate_2024-01-14_09-15-42.log
└── ...
```

### Log-Inhalt
- Zeitstempel aller Aktionen
- Erfolgreiche Updates
- Fehlermeldungen
- System-Informationen

## 🔄 Automatisierung

### Geplante Aufgabe erstellen
```batch
# Wöchentlich Sonntags um 2:00 Uhr
schtasks /create /tn "VDI Auto Update" /tr "C:\Temp\scripting\VDI\AutoUpdate-Master.bat" /sc weekly /d SUN /st 02:00 /rl HIGHEST

# Täglich um 18:00 Uhr (Quick Update)
schtasks /create /tn "VDI Quick Update" /tr "C:\Temp\scripting\VDI\Quick-Update.bat" /sc daily /st 18:00 /rl HIGHEST
```

### Startup-Script
Fügen Sie zu Autostart hinzu:
```
Win + R → shell:startup
Verknüpfung zu Quick-Update.bat erstellen
```

## 🆘 Troubleshooting

### Häufige Probleme

**Problem:** "Winget nicht gefunden"
**Lösung:** Microsoft Store → "App Installer" installieren

**Problem:** "PowerShell Execution Policy"
**Lösung:** Script setzt automatisch RemoteSigned

**Problem:** "Zugriff verweigert"
**Lösung:** Als Administrator ausführen

**Problem:** "Updates schlagen fehl"
**Lösung:** Internet-Verbindung prüfen, Antivirus deaktivieren

### Debug-Modus
Entfernen Sie `>nul 2>&1` aus Scripts für detaillierte Ausgabe

## 📈 Performance

### Geschwindigkeit
- **Quick Update:** ~2-5 Minuten
- **Vollständiges Update:** ~15-30 Minuten
- **Abhängig von:** Internet-Geschwindigkeit, Anzahl Updates

### Ressourcen
- **CPU:** Niedrig-mittel während Updates
- **RAM:** ~200-500 MB
- **Netzwerk:** Abhängig von Update-Größe

## 🔮 Zukünftige Erweiterungen

- GUI-Interface
- Update-Scheduler
- E-Mail-Benachrichtigungen
- Rollback-Funktionalität
- Netzwerk-Deployment

---

**Entwickelt für effiziente VDI-Umgebungen und System-Administration**
