-- Advanced Admin Script - Client Main
-- Author: Your Name
-- Version: 1.0.0

local AdminClient = {}
AdminClient.isGodMode = false
AdminClient.isNoclip = false
AdminClient.isInvisible = false
AdminClient.isFrozen = false
AdminClient.menuOpen = false

-- Initialize client
Citizen.CreateThread(function()
    print("^2[Advanced Admin]^7 Client script loaded")
end)

-- God mode thread
Citizen.CreateThread(function()
    while true do
        if AdminClient.isGodMode then
            local ped = PlayerPedId()
            SetEntityInvincible(ped, true)
            SetPedCanRagdoll(ped, false)
            SetEntityHealth(ped, GetEntityMaxHealth(ped))
            SetPedArmour(ped, GetPlayerMaxArmour(PlayerId()))
        end
        Citizen.Wait(1000)
    end
end)

-- Freeze thread
Citizen.CreateThread(function()
    while true do
        if AdminClient.isFrozen then
            local ped = PlayerPedId()
            FreezeEntityPosition(ped, true)
            SetEntityInvincible(ped, true)
        end
        Citizen.Wait(100)
    end
end)

-- Event Handlers
RegisterNetEvent('admin:notification')
AddEventHandler('admin:notification', function(message, type)
    ShowNotification(message, type)
end)

RegisterNetEvent('admin:teleport')
AddEventHandler('admin:teleport', function(x, y, z)
    local ped = PlayerPedId()
    SetEntityCoords(ped, x, y, z, false, false, false, true)
    ShowNotification("Teleportiert zu " .. x .. ", " .. y .. ", " .. z, 'success')
end)

RegisterNetEvent('admin:teleportToPlayer')
AddEventHandler('admin:teleportToPlayer', function(targetId)
    local targetPed = GetPlayerPed(GetPlayerFromServerId(targetId))
    if targetPed and targetPed ~= 0 then
        local coords = GetEntityCoords(targetPed)
        local ped = PlayerPedId()
        SetEntityCoords(ped, coords.x, coords.y, coords.z, false, false, false, true)
    end
end)

RegisterNetEvent('admin:teleportPlayerHere')
AddEventHandler('admin:teleportPlayerHere', function(adminId)
    local adminPed = GetPlayerPed(GetPlayerFromServerId(adminId))
    if adminPed and adminPed ~= 0 then
        local coords = GetEntityCoords(adminPed)
        local ped = PlayerPedId()
        SetEntityCoords(ped, coords.x, coords.y, coords.z, false, false, false, true)
    end
end)

RegisterNetEvent('admin:heal')
AddEventHandler('admin:heal', function()
    local ped = PlayerPedId()
    SetEntityHealth(ped, GetEntityMaxHealth(ped))
    ClearPedBloodDamage(ped)
    ShowNotification("Geheilt", 'success')
end)

RegisterNetEvent('admin:armor')
AddEventHandler('admin:armor', function()
    local ped = PlayerPedId()
    SetPedArmour(ped, GetPlayerMaxArmour(PlayerId()))
    ShowNotification("Rüstung aufgefüllt", 'success')
end)

RegisterNetEvent('admin:toggleGod')
AddEventHandler('admin:toggleGod', function(enabled)
    AdminClient.isGodMode = enabled
    local ped = PlayerPedId()
    
    if enabled then
        SetEntityInvincible(ped, true)
        SetPedCanRagdoll(ped, false)
        ShowNotification("God Mode aktiviert", 'info')
    else
        SetEntityInvincible(ped, false)
        SetPedCanRagdoll(ped, true)
        ShowNotification("God Mode deaktiviert", 'info')
    end
end)

RegisterNetEvent('admin:toggleNoclip')
AddEventHandler('admin:toggleNoclip', function(enabled)
    AdminClient.isNoclip = enabled
    if enabled then
        StartNoclip()
        ShowNotification("NoClip aktiviert", 'info')
    else
        StopNoclip()
        ShowNotification("NoClip deaktiviert", 'info')
    end
end)

RegisterNetEvent('admin:freeze')
AddEventHandler('admin:freeze', function(enabled)
    AdminClient.isFrozen = enabled
    local ped = PlayerPedId()
    
    if enabled then
        FreezeEntityPosition(ped, true)
        SetEntityInvincible(ped, true)
    else
        FreezeEntityPosition(ped, false)
        SetEntityInvincible(ped, false)
    end
end)

RegisterNetEvent('admin:spawnVehicle')
AddEventHandler('admin:spawnVehicle', function(vehicleName)
    SpawnVehicle(vehicleName)
end)

RegisterNetEvent('admin:deleteVehicle')
AddEventHandler('admin:deleteVehicle', function()
    DeleteCurrentVehicle()
end)

RegisterNetEvent('admin:fixVehicle')
AddEventHandler('admin:fixVehicle', function()
    FixCurrentVehicle()
end)

RegisterNetEvent('admin:setWeather')
AddEventHandler('admin:setWeather', function(weather)
    SetWeatherTypeNowPersist(weather)
    SetWeatherTypeNow(weather)
    ShowNotification("Wetter geändert zu: " .. weather, 'info')
end)

RegisterNetEvent('admin:setTime')
AddEventHandler('admin:setTime', function(hour, minute)
    NetworkOverrideClockTime(hour, minute, 0)
    ShowNotification(string.format("Zeit geändert zu: %02d:%02d", hour, minute), 'info')
end)

RegisterNetEvent('admin:openMenu')
AddEventHandler('admin:openMenu', function()
    OpenAdminMenu()
end)

RegisterNetEvent('admin:revive')
AddEventHandler('admin:revive', function()
    local ped = PlayerPedId()
    local coords = GetEntityCoords(ped)

    -- Revive player
    NetworkResurrectLocalPlayer(coords.x, coords.y, coords.z, GetEntityHeading(ped), true, false)
    SetPlayerInvincible(ped, false)
    ClearPedBloodDamage(ped)

    ShowNotification("Du wurdest wiederbelebt", 'success')
end)

RegisterNetEvent('admin:toggleInvisible')
AddEventHandler('admin:toggleInvisible', function(enabled)
    AdminClient.isInvisible = enabled
    local ped = PlayerPedId()

    SetEntityVisible(ped, not enabled, false)
    SetLocalPlayerVisibleLocally(enabled)

    local status = enabled and 'aktiviert' or 'deaktiviert'
    ShowNotification(string.format("Unsichtbarkeit %s", status), 'info')
end)

-- Functions
function ShowNotification(message, type)
    SetNotificationTextEntry("STRING")
    AddTextComponentString(message)
    DrawNotification(false, false)
    
    -- Also send to NUI for better notifications
    SendNUIMessage({
        type = "notification",
        message = message,
        notificationType = type or 'info'
    })
end

function SpawnVehicle(vehicleName)
    local ped = PlayerPedId()
    local coords = GetEntityCoords(ped)
    local heading = GetEntityHeading(ped)
    
    local vehicleHash = GetHashKey(vehicleName)
    
    if not IsModelInCdimage(vehicleHash) or not IsModelAVehicle(vehicleHash) then
        ShowNotification("Ungültiges Fahrzeug: " .. vehicleName, 'error')
        return
    end
    
    RequestModel(vehicleHash)
    while not HasModelLoaded(vehicleHash) do
        Citizen.Wait(100)
    end
    
    local vehicle = CreateVehicle(vehicleHash, coords.x, coords.y, coords.z, heading, true, false)
    SetPedIntoVehicle(ped, vehicle, -1)
    SetEntityAsNoLongerNeeded(vehicle)
    SetModelAsNoLongerNeeded(vehicleHash)
    
    ShowNotification("Fahrzeug gespawnt: " .. vehicleName, 'success')
end

function DeleteCurrentVehicle()
    local ped = PlayerPedId()
    local vehicle = GetVehiclePedIsIn(ped, false)
    
    if vehicle and vehicle ~= 0 then
        DeleteEntity(vehicle)
        ShowNotification("Fahrzeug gelöscht", 'success')
    else
        ShowNotification("Du bist in keinem Fahrzeug", 'error')
    end
end

function FixCurrentVehicle()
    local ped = PlayerPedId()
    local vehicle = GetVehiclePedIsIn(ped, false)
    
    if vehicle and vehicle ~= 0 then
        SetVehicleFixed(vehicle)
        SetVehicleDeformationFixed(vehicle)
        SetVehicleUndriveable(vehicle, false)
        SetVehicleEngineOn(vehicle, true, true)
        ShowNotification("Fahrzeug repariert", 'success')
    else
        ShowNotification("Du bist in keinem Fahrzeug", 'error')
    end
end

function OpenAdminMenu()
    if AdminClient.menuOpen then
        return
    end
    
    AdminClient.menuOpen = true
    SetNuiFocus(true, true)
    SendNUIMessage({
        type = "openMenu"
    })
end

-- NUI Callbacks
RegisterNUICallback('closeMenu', function(data, cb)
    AdminClient.menuOpen = false
    SetNuiFocus(false, false)
    cb('ok')
end)

RegisterNUICallback('executeCommand', function(data, cb)
    if data.command then
        ExecuteCommand(data.command)
    end
    cb('ok')
end)

-- Key bindings (optional)
RegisterKeyMapping('adminmenu', 'Open Admin Menu', 'keyboard', 'F6')

-- Global access
_G.AdminClient = AdminClient
